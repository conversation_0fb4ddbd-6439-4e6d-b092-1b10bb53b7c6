package billing

import (
	"fmt"
	"net/http"
	"strconv"

	"git.uozi.org/uozi/potato-billing-api/internal/billing"
	"git.uozi.org/uozi/potato-billing-api/model"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
)

// RegisterPriceRuleAPI 初始化定价规则 API
func RegisterPriceRuleAPI(g *gin.RouterGroup) {
	g.GET("/pricing_rules", GetPricingRuleList)
	g.POST("/pricing_rules", CreatePricingRule)
	g.GET("/pricing_rules/:id", GetPricingRule)
	g.POST("/pricing_rules/:id", UpdatePricingRule)
	g.DELETE("/pricing_rules/:id", DeletePricingRule)
}

// GetPricingRuleList 获取定价规则列表
func GetPricingRuleList(c *gin.Context) {
	// 解析查询参数
	var query billing.PricingRuleListQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 获取计费服务
	billingService := billing.GetBillingService()
	if billingService == nil {
		cosy.ErrHandler(c, fmt.Errorf("计费服务不可用"))
		return
	}

	pricingService := billingService.GetPricingService()
	if pricingService == nil {
		cosy.ErrHandler(c, fmt.Errorf("定价服务不可用"))
		return
	}

	// 调用 internal 模块获取定价规则列表
	response, err := pricingService.GetPricingRuleList(&query)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, response)
}

// CreatePricingRule 创建定价规则
func CreatePricingRule(c *gin.Context) {
	var rule model.PricingRule
	if err := c.ShouldBindJSON(&rule); err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 获取计费服务
	billingService := billing.GetBillingService()
	if billingService == nil {
		cosy.ErrHandler(c, fmt.Errorf("计费服务不可用"))
		return
	}

	pricingService := billingService.GetPricingService()
	if pricingService == nil {
		cosy.ErrHandler(c, fmt.Errorf("定价服务不可用"))
		return
	}

	// 调用 internal 模块创建定价规则
	err := pricingService.CreatePricingRule(&rule)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, rule)
}

// GetPricingRule 获取单个定价规则
func GetPricingRule(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		cosy.ErrHandler(c, fmt.Errorf("无效的规则ID"))
		return
	}

	// 获取计费服务
	billingService := billing.GetBillingService()
	if billingService == nil {
		cosy.ErrHandler(c, fmt.Errorf("计费服务不可用"))
		return
	}

	pricingService := billingService.GetPricingService()
	if pricingService == nil {
		cosy.ErrHandler(c, fmt.Errorf("定价服务不可用"))
		return
	}

	// 调用 internal 模块获取定价规则
	rule, err := pricingService.GetPricingRule(id)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, rule)
}

// UpdatePricingRule 更新定价规则
func UpdatePricingRule(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		cosy.ErrHandler(c, fmt.Errorf("无效的规则ID"))
		return
	}

	var updates map[string]interface{}
	if err := c.ShouldBindJSON(&updates); err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 获取计费服务
	billingService := billing.GetBillingService()
	if billingService == nil {
		cosy.ErrHandler(c, fmt.Errorf("计费服务不可用"))
		return
	}

	pricingService := billingService.GetPricingService()
	if pricingService == nil {
		cosy.ErrHandler(c, fmt.Errorf("定价服务不可用"))
		return
	}

	// 调用 internal 模块更新定价规则
	err = pricingService.UpdatePricingRule(id, updates)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 返回更新后的规则
	rule, err := pricingService.GetPricingRule(id)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, rule)
}

// DeletePricingRule 删除定价规则
func DeletePricingRule(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		cosy.ErrHandler(c, fmt.Errorf("无效的规则ID"))
		return
	}

	// 获取计费服务
	billingService := billing.GetBillingService()
	if billingService == nil {
		cosy.ErrHandler(c, fmt.Errorf("计费服务不可用"))
		return
	}

	pricingService := billingService.GetPricingService()
	if pricingService == nil {
		cosy.ErrHandler(c, fmt.Errorf("定价服务不可用"))
		return
	}

	// 调用 internal 模块删除定价规则
	err = pricingService.DeletePricingRule(id)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "定价规则删除成功",
	})
}
