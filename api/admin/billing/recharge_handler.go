package billing

import (
	"fmt"
	"net/http"
	"strconv"

	"git.uozi.org/uozi/potato-billing-api/api"
	"git.uozi.org/uozi/potato-billing-api/internal/billing"
	"git.uozi.org/uozi/potato-billing-api/model"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy"
)

// 类型定义已移动到 internal/billing 模块中

// RegisterRechargeAPI 初始化充值API
func RegisterRechargeAPI(g *gin.RouterGroup) {
	// 自定义路由
	g.GET("/recharge_records", GetRechargeList)
	g.POST("/recharge_records", CreateRecharge)
	g.POST("/recharge_records/:id/confirm", ConfirmRecharge)
	g.POST("/recharge_records/:id/cancel", CancelRecharge)
	g.GET("/recharge_records/stats", GetRechargeStats)
	g.GET("/recharge_records/user-balances", GetUserBalances)
}

// GetRechargeList 获取充值记录列表
func GetRechargeList(c *gin.Context) {
	cosy.
		Core[model.RechargeRecord](c).
		SetEqual("user_id", "type").
		SetPreloads("User").
		PagingList()
}

// CreateRecharge 创建充值记录（管理员充值）
func CreateRecharge(c *gin.Context) {
	var req billing.CreateRechargeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 获取操作员信息
	operator := api.CurrentUser(c)

	// 获取充值服务
	billingService := billing.GetBillingService()
	if billingService == nil {
		cosy.ErrHandler(c, fmt.Errorf("计费服务不可用"))
		return
	}

	rechargeService := billing.NewRechargeService(billingService.GetAppService())

	// 调用 internal 模块创建充值记录
	err := rechargeService.CreateRecharge(c.Request.Context(), &req, operator.ID)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "充值成功",
		"amount":  req.Amount,
	})
}

// ConfirmRecharge 确认充值（用于处理第三方支付回调）
func ConfirmRecharge(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 获取充值服务
	billingService := billing.GetBillingService()
	if billingService == nil {
		cosy.ErrHandler(c, fmt.Errorf("计费服务不可用"))
		return
	}

	rechargeService := billing.NewRechargeService(billingService.GetAppService())

	// 调用 internal 模块确认充值
	err = rechargeService.ConfirmRecharge(c.Request.Context(), id)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "充值确认成功",
	})
}

// CancelRecharge 取消充值
func CancelRecharge(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 获取充值服务
	billingService := billing.GetBillingService()
	if billingService == nil {
		cosy.ErrHandler(c, fmt.Errorf("计费服务不可用"))
		return
	}

	rechargeService := billing.NewRechargeService(billingService.GetAppService())

	// 调用 internal 模块取消充值
	err = rechargeService.CancelRecharge(c.Request.Context(), id)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "充值已取消",
	})
}

// GetRechargeStats 获取充值统计数据
func GetRechargeStats(c *gin.Context) {
	// 获取充值服务
	billingService := billing.GetBillingService()
	if billingService == nil {
		cosy.ErrHandler(c, fmt.Errorf("计费服务不可用"))
		return
	}

	rechargeService := billing.NewRechargeService(billingService.GetAppService())

	// 调用 internal 模块获取充值统计
	stats, err := rechargeService.GetRechargeStats(c.Request.Context())
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, stats)
}

// GetUserBalances 获取用户余额排行
func GetUserBalances(c *gin.Context) {
	// 解析分页参数
	page := cast.ToInt(c.DefaultQuery("page", "1"))
	pageSize := cast.ToInt(c.DefaultQuery("pageSize", "20"))

	// 获取充值服务
	billingService := billing.GetBillingService()
	if billingService == nil {
		cosy.ErrHandler(c, fmt.Errorf("计费服务不可用"))
		return
	}

	rechargeService := billing.NewRechargeService(billingService.GetAppService())

	// 调用 internal 模块获取用户余额信息
	userBalances, total, err := rechargeService.GetUserBalances(c.Request.Context(), page, pageSize)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 构建响应
	response := map[string]interface{}{
		"data": userBalances,
		"pagination": map[string]interface{}{
			"total":        total,
			"per_page":     pageSize,
			"current_page": page,
			"total_pages":  (total + int64(pageSize) - 1) / int64(pageSize),
		},
	}

	c.JSON(http.StatusOK, response)
}
