package app

import (
	"net/http"
	"strconv"

	"git.uozi.org/uozi/potato-billing-api/api"
	appService "git.uozi.org/uozi/potato-billing-api/internal/app"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
)

// 删除重复的类型定义，使用 internal/app 模块中的类型

// GetApplicationList 获取应用列表
func GetApplicationList(c *gin.Context) {
	user := api.CurrentUser(c)

	// 解析查询参数
	var query appService.AppListQuery
	query.UserID = user.ID
	if err := c.ShouldBindQuery(&query); err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 设置默认分页参数
	if query.Page < 1 {
		query.Page = 1
	}
	if query.PageSize < 1 || query.PageSize > 100 {
		query.PageSize = 20
	}

	// 调用 internal 模块获取应用列表
	response, err := appService.GetAppList(c.Request.Context(), &query)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSO<PERSON>(http.StatusOK, response)
}

// GetApplication 获取应用详情
func GetApplication(c *gin.Context) {
	user := api.CurrentUser(c)
	idStr := c.Param("id")

	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 调用 internal 模块获取应用详情
	app, err := appService.GetApp(c.Request.Context(), id, user.ID)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, app)
}

// CreateApplication 创建应用
func CreateApplication(c *gin.Context) {
	user := api.CurrentUser(c)

	var req appService.CreateAppRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 调用 internal 模块创建应用
	app, err := appService.CreateApp(c.Request.Context(), user.ID, &req)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, app)
}

// UpdateApplication 更新应用
func UpdateApplication(c *gin.Context) {
	user := api.CurrentUser(c)
	idStr := c.Param("id")

	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	var req appService.UpdateAppRequest
	if bindErr := c.ShouldBindJSON(&req); bindErr != nil {
		cosy.ErrHandler(c, bindErr)
		return
	}

	// 调用 internal 模块更新应用
	app, err := appService.UpdateApp(c.Request.Context(), id, user.ID, &req)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, app)
}

// DeleteApplication 删除应用
func DeleteApplication(c *gin.Context) {
	user := api.CurrentUser(c)
	idStr := c.Param("id")

	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 调用 internal 模块删除应用
	err = appService.DeleteApp(c.Request.Context(), id, user.ID)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "应用删除成功"})
}

// RegenerateApiKey 重新生成API Key
func RegenerateApiKey(c *gin.Context) {
	user := api.CurrentUser(c)
	idStr := c.Param("id")

	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 调用 internal 模块重新生成API Key
	app, err := appService.RegenerateAPIKey(c.Request.Context(), id, user.ID)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, app)
}
