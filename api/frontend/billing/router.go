package billing

import (
	"github.com/gin-gonic/gin"
)

func RegisterStatsRouter(r *gin.RouterGroup) {
	// 现有接口
	r.GET("/module-stats", GetModuleStats)
	r.GET("/module-trends", GetModuleTrends)

	// 新增接口
	r.GET("/stats", GetBillingStats)
	r.GET("/trends", GetUsageTrends)
	r.GET("/services", GetServiceUsage)
	r.GET("/periods", GetBillingPeriods)
	r.GET("/export", ExportBillingReport)
}

func RegisterApplicationBillingRouter(r *gin.RouterGroup) {
	// 应用级别的billing接口
	r.GET("/applications/:app_id/billing", GetApplicationBilling)
}
