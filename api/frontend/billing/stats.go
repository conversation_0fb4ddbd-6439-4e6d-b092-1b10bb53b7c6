package billing

import (
	"context"
	"fmt"
	"net/http"

	"git.uozi.org/uozi/potato-billing-api/api"
	"git.uozi.org/uozi/potato-billing-api/internal/billing"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy"
)

// GetModuleStats 获取按模块统计数据
func GetModuleStats(c *gin.Context) {
	// 获取当前用户
	currentUser := api.CurrentUser(c)
	if currentUser == nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未登录"})
		return
	}

	period := c.DefaultQuery("period", "30d")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")
	appID := cast.ToUint64(c.Query("app_id"))
	module := c.<PERSON><PERSON>("module")

	// 如果指定了appID，验证该应用是否属于当前用户
	if appID != 0 {
		if !isUserApp(c.Request.Context(), currentUser.ID, appID) {
			c.JSON(http.StatusForbidden, gin.H{"error": "无权访问该应用数据"})
			return
		}
	}

	// 获取计费服务
	billingService := billing.GetBillingService()
	if billingService == nil {
		cosy.ErrHandler(c, fmt.Errorf("计费服务不可用"))
		return
	}

	// 获取前端统计服务
	frontendStatsService := billing.NewFrontendStatsService(billingService.GetStatService())

	// 调用 internal 模块获取模块统计数据
	stats, err := frontendStatsService.GetModuleStats(c.Request.Context(), currentUser.ID, appID, period, startDate, endDate, module)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, stats)
}

// GetModuleTrends 获取按模块趋势数据
func GetModuleTrends(c *gin.Context) {
	// 获取当前用户
	currentUser := api.CurrentUser(c)
	if currentUser == nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未登录"})
		return
	}

	period := c.DefaultQuery("period", "30d")
	metric := c.DefaultQuery("metric", "cost")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")
	appID := cast.ToUint64(c.Query("app_id"))

	// 如果指定了appID，验证该应用是否属于当前用户
	if appID != 0 {
		if !isUserApp(c.Request.Context(), currentUser.ID, appID) {
			c.JSON(http.StatusForbidden, gin.H{"error": "无权访问该应用数据"})
			return
		}
	}

	// 获取计费服务
	billingService := billing.GetBillingService()
	if billingService == nil {
		cosy.ErrHandler(c, fmt.Errorf("计费服务不可用"))
		return
	}

	// 获取前端统计服务
	frontendStatsService := billing.NewFrontendStatsService(billingService.GetStatService())

	// 调用 internal 模块获取模块趋势数据
	trends, err := frontendStatsService.GetModuleTrends(c.Request.Context(), currentUser.ID, appID, period, metric, startDate, endDate)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, trends)
}

// GetBillingStats 获取计费统计概览
func GetBillingStats(c *gin.Context) {
	// 获取当前用户
	currentUser := api.CurrentUser(c)
	if currentUser == nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未登录"})
		return
	}

	period := c.DefaultQuery("period", "30d")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")
	appID := cast.ToUint64(c.Query("app_id"))

	// 如果指定了appID，验证该应用是否属于当前用户
	if appID != 0 {
		if !isUserApp(c.Request.Context(), currentUser.ID, appID) {
			c.JSON(http.StatusForbidden, gin.H{"error": "无权访问该应用数据"})
			return
		}
	}

	// 获取计费服务
	billingService := billing.GetBillingService()
	if billingService == nil {
		cosy.ErrHandler(c, fmt.Errorf("计费服务不可用"))
		return
	}

	// 获取前端统计服务
	frontendStatsService := billing.NewFrontendStatsService(billingService.GetStatService())

	// 调用 internal 模块获取计费统计概览
	stats, err := frontendStatsService.GetBillingStats(c.Request.Context(), currentUser.ID, appID, period, startDate, endDate)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, stats)
}

// GetServiceUsage 获取服务使用分布
func GetServiceUsage(c *gin.Context) {
	// 获取当前用户
	currentUser := api.CurrentUser(c)
	if currentUser == nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未登录"})
		return
	}

	period := c.DefaultQuery("period", "30d")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")
	appID := cast.ToUint64(c.Query("app_id"))

	// 如果指定了appID，验证该应用是否属于当前用户
	if appID != 0 {
		if !isUserApp(c.Request.Context(), currentUser.ID, appID) {
			c.JSON(http.StatusForbidden, gin.H{"error": "无权访问该应用数据"})
			return
		}
	}

	// 获取计费服务
	billingService := billing.GetBillingService()
	if billingService == nil {
		cosy.ErrHandler(c, fmt.Errorf("计费服务不可用"))
		return
	}

	// 获取前端统计服务
	frontendStatsService := billing.NewFrontendStatsService(billingService.GetStatService())

	// 调用 internal 模块获取服务使用分布
	serviceUsage, err := frontendStatsService.GetServiceUsage(c.Request.Context(), currentUser.ID, appID, period, startDate, endDate)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, serviceUsage)
}

// GetBillingPeriods 获取计费周期数据
func GetBillingPeriods(c *gin.Context) {
	// 获取当前用户
	currentUser := api.CurrentUser(c)
	if currentUser == nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未登录"})
		return
	}

	// 解析查询参数
	var query billing.BillingPeriodsQuery
	query.UserID = currentUser.ID
	if err := c.ShouldBindQuery(&query); err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 设置默认分页参数
	if query.Page < 1 {
		query.Page = 1
	}
	if query.PageSize < 1 || query.PageSize > 100 {
		query.PageSize = 20
	}

	// 如果指定了appID，验证该应用是否属于当前用户
	if query.AppID != 0 {
		if !isUserApp(c.Request.Context(), currentUser.ID, query.AppID) {
			c.JSON(http.StatusForbidden, gin.H{"error": "无权访问该应用数据"})
			return
		}
	}

	// 获取计费服务
	billingService := billing.GetBillingService()
	if billingService == nil {
		cosy.ErrHandler(c, fmt.Errorf("计费服务不可用"))
		return
	}

	// 获取前端统计服务
	frontendStatsService := billing.NewFrontendStatsService(billingService.GetStatService())

	// 调用 internal 模块获取计费周期数据
	response, err := frontendStatsService.GetBillingPeriods(c.Request.Context(), &query)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetUsageTrends 获取使用趋势数据
func GetUsageTrends(c *gin.Context) {
	// 获取当前用户
	currentUser := api.CurrentUser(c)
	if currentUser == nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未登录"})
		return
	}

	period := c.DefaultQuery("period", "30d")
	metric := c.DefaultQuery("metric", "cost")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")
	appID := cast.ToUint64(c.Query("app_id"))

	// 如果指定了appID，验证该应用是否属于当前用户
	if appID != 0 {
		if !isUserApp(c.Request.Context(), currentUser.ID, appID) {
			c.JSON(http.StatusForbidden, gin.H{"error": "无权访问该应用数据"})
			return
		}
	}

	// 获取计费服务
	billingService := billing.GetBillingService()
	if billingService == nil {
		cosy.ErrHandler(c, fmt.Errorf("计费服务不可用"))
		return
	}

	// 获取前端统计服务
	frontendStatsService := billing.NewFrontendStatsService(billingService.GetStatService())

	// 调用 internal 模块获取趋势数据
	trends, err := frontendStatsService.GetModuleTrends(c.Request.Context(), currentUser.ID, appID, period, metric, startDate, endDate)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 转换为前端需要的格式
	response := billing.UsageTrendResponse{
		Labels: trends.Labels,
		Datasets: []billing.TrendData{
			{
				Label: getMetricLabel(metric),
				Data:  aggregateDatasets(trends.Datasets),
			},
		},
	}

	c.JSON(http.StatusOK, response)
}

// GetApplicationBilling 获取应用的计费数据
func GetApplicationBilling(c *gin.Context) {
	// 获取当前用户
	currentUser := api.CurrentUser(c)
	if currentUser == nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未登录"})
		return
	}

	appID := cast.ToUint64(c.Param("app_id"))
	period := c.DefaultQuery("period", "30d")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	// 验证应用是否属于当前用户
	if !isUserApp(c.Request.Context(), currentUser.ID, appID) {
		c.JSON(http.StatusForbidden, gin.H{"error": "无权访问该应用数据"})
		return
	}

	// 获取计费服务
	billingService := billing.GetBillingService()
	if billingService == nil {
		cosy.ErrHandler(c, fmt.Errorf("计费服务不可用"))
		return
	}

	// 获取前端统计服务
	frontendStatsService := billing.NewFrontendStatsService(billingService.GetStatService())

	// 获取应用的统计数据
	stats, err := frontendStatsService.GetBillingStats(c.Request.Context(), currentUser.ID, appID, period, startDate, endDate)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 获取趋势数据
	trends, err := frontendStatsService.GetModuleTrends(c.Request.Context(), currentUser.ID, appID, period, "cost", startDate, endDate)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 获取服务使用分布
	serviceUsage, err := frontendStatsService.GetServiceUsage(c.Request.Context(), currentUser.ID, appID, period, startDate, endDate)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 构建响应
	response := billing.ApplicationBillingResponse{
		Stats: *stats,
		Trends: []billing.UsageTrendResponse{
			{
				Labels: trends.Labels,
				Datasets: []billing.TrendData{
					{
						Label: "费用",
						Data:  aggregateDatasets(trends.Datasets),
					},
				},
			},
		},
		Services: []billing.ServiceUsageResponse{*serviceUsage},
	}

	c.JSON(http.StatusOK, response)
}

// ExportBillingReport 导出计费报告
func ExportBillingReport(c *gin.Context) {
	// 获取当前用户
	currentUser := api.CurrentUser(c)
	if currentUser == nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未登录"})
		return
	}

	period := c.DefaultQuery("period", "30d")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")
	appID := cast.ToUint64(c.Query("app_id"))

	// 如果指定了appID，验证该应用是否属于当前用户
	if appID != 0 {
		if !isUserApp(c.Request.Context(), currentUser.ID, appID) {
			c.JSON(http.StatusForbidden, gin.H{"error": "无权访问该应用数据"})
			return
		}
	}

	// 获取计费服务
	billingService := billing.GetBillingService()
	if billingService == nil {
		cosy.ErrHandler(c, fmt.Errorf("计费服务不可用"))
		return
	}

	// 获取前端统计服务
	frontendStatsService := billing.NewFrontendStatsService(billingService.GetStatService())

	// 获取统计数据
	stats, err := frontendStatsService.GetModuleStats(c.Request.Context(), currentUser.ID, appID, period, startDate, endDate, "")
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 生成CSV内容
	csvContent := "模块,调用次数,使用量,总费用,平均单价\n"
	csvContent += fmt.Sprintf("LLM,%d,%d,%.2f,%.4f\n", stats.LLM.Calls, stats.LLM.Usage, stats.LLM.Cost, stats.LLM.AvgCostPerCall)
	csvContent += fmt.Sprintf("TTS,%d,%d,%.2f,%.4f\n", stats.TTS.Calls, stats.TTS.Usage, stats.TTS.Cost, stats.TTS.AvgCostPerCall)
	csvContent += fmt.Sprintf("ASR,%d,%d,%.2f,%.4f\n", stats.ASR.Calls, stats.ASR.Usage, stats.ASR.Cost, stats.ASR.AvgCostPerCall)

	// 设置响应头
	c.Header("Content-Type", "text/csv")
	c.Header("Content-Disposition", "attachment; filename=billing_report.csv")

	c.String(http.StatusOK, csvContent)
}

// 辅助函数

// getMetricLabel 获取指标标签
func getMetricLabel(metric string) string {
	switch metric {
	case "calls":
		return "调用次数"
	case "usage":
		return "使用量"
	case "cost":
		return "费用"
	default:
		return "调用次数"
	}
}

// aggregateDatasets 聚合数据集
func aggregateDatasets(datasets []billing.ModuleTrendDataset) []float64 {
	if len(datasets) == 0 {
		return []float64{}
	}

	// 如果只有一个数据集，直接返回
	if len(datasets) == 1 {
		return datasets[0].Data
	}

	// 聚合多个数据集
	maxLen := 0
	for _, dataset := range datasets {
		if len(dataset.Data) > maxLen {
			maxLen = len(dataset.Data)
		}
	}

	result := make([]float64, maxLen)
	for i := 0; i < maxLen; i++ {
		sum := 0.0
		for _, dataset := range datasets {
			if i < len(dataset.Data) {
				sum += dataset.Data[i]
			}
		}
		result[i] = sum
	}

	return result
}

// isUserApp 检查应用是否属于指定用户
func isUserApp(ctx context.Context, userID, appID uint64) bool {
	// 获取计费服务
	billingService := billing.GetBillingService()
	if billingService == nil {
		return false
	}

	// 获取前端统计服务
	frontendStatsService := billing.NewFrontendStatsService(billingService.GetStatService())

	// 调用 internal 模块检查应用所有权
	return frontendStatsService.IsUserApp(ctx, userID, appID)
}
