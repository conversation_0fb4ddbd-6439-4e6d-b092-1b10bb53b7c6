package auth

import (
	"net/http"

	"git.uozi.org/uozi/potato-billing-api/api"
	"git.uozi.org/uozi/potato-billing-api/internal/auth"
	"git.uozi.org/uozi/potato-billing-api/internal/user"
	"git.uozi.org/uozi/potato-billing-api/model"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
)

// LoginResponse API 层的登录响应结构（保持向后兼容）
type LoginResponse struct {
	Message string      `json:"message"`
	Error   string      `json:"error,omitempty"`
	Code    int         `json:"code"`
	Token   string      `json:"token,omitempty"`
	User    *model.User `json:"user,omitempty"`
}

func Login(c *gin.Context) {
	// 解析请求数据
	var req auth.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 调用 internal 模块进行登录
	response, err := auth.Login(c.Request.Context(), c.<PERSON>(), c.<PERSON>("X-Fingerprint"), &req)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 如果登录成功，生成 token
	if response.Code == 200 && response.User != nil {
		token, err := api.GenerateToken(response.User)
		if err != nil {
			cosy.ErrHandler(c, err)
			return
		}
		response.Token = token
	}

	// 根据响应码设置 HTTP 状态码
	var httpStatus int
	switch response.Code {
	case 200:
		httpStatus = http.StatusOK
	case 429:
		httpStatus = http.StatusTooManyRequests
	default:
		httpStatus = http.StatusNotAcceptable
	}

	c.JSON(httpStatus, response)
}

func Logout(c *gin.Context) {
	token := user.CurrentToken(c)

	// 调用 internal 模块进行登出
	err := auth.Logout(token)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusNoContent, nil)
}
