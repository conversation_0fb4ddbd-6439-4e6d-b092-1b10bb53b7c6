package user

import (
	"net/http"

	"git.uozi.org/uozi/potato-billing-api/api"
	"git.uozi.org/uozi/potato-billing-api/internal/user"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
)

func GetCurrentUser(c *gin.Context) {
	currentUser := api.CurrentUser(c)

	// 调用 internal 模块获取用户信息（包含头像）
	userWithAvatar, err := user.GetCurrentUserWithAvatar(currentUser.ID)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSO<PERSON>(http.StatusOK, userWithAvatar)
}

func UpdateCurrentUser(c *gin.Context) {
	currentUser := api.CurrentUser(c)

	// 解析请求数据
	var req user.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 调用 internal 模块更新用户信息
	updatedUser, err := user.UpdateCurrentUser(c.Request.Context(), currentUser.ID, &req)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, updatedUser)
}
