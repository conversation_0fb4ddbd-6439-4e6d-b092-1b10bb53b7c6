import type { App, AppStatus, ModelBase, PaginationResponse, QuotaPackage, ServiceModule, TimePeriod, Unit, UsageLog, User } from '@billing/common'
import { extendCurdApi, http, useCurdApi } from '@uozi-admin/request'

// Key状态响应
export interface AppStatusResponse extends ModelBase {
  api_key: string
  status: AppStatus
  available: boolean
  used: number
  user?: User
  quotas?: QuotaPackage[]
}

// 用量统计
export interface UsageStats {
  period: TimePeriod
  start_time: number
  end_time: number
  total_cost: number // 总成本（可以相加）
  total_count: number // 总请求数（可以相加）
  by_module: ModuleStats[] // 按模块分开的统计
}

export interface ModuleStats {
  module: ServiceModule
  total_usage: number // 改名为usage，因为不同模块单位不同
  total_cost: number
  count: number
  unit: Unit // 添加单位字段，如 'tokens', 'characters', 'seconds'
}

// 更新配额请求
export interface UpdateQuotaRequest {
  module: ServiceModule
  quota: number
  expires_at?: number
  description: string
}

// 创建配额请求
export interface CreateQuotaRequest {
  module: ServiceModule
  quota: number
  expires_at?: number
  model_name?: string
  description?: string
}

// API Key概览统计
export interface KeyOverviewStats {
  total_apps: number // 总应用数量
  active_apps: number // 活跃应用数量
  blocked_apps: number // 阻止应用数量
  apps_with_quota: number // 有配额的应用数量
  apps_with_balance: number // 用户有余额的应用数量
  total_quota_usage: number // 总配额使用率
  avg_quota_usage: number // 平均配额使用率
  module_stats: ModuleStat[] // 按模块统计
}

// 模块统计
export interface ModuleStat {
  module: string // 模块名称
  name: string // 模块显示名称
  app_count: number // 应用数量
  active_count: number // 活跃应用数量
  avg_usage: number // 平均使用率
}

export const appApi = extendCurdApi(useCurdApi<App>('/admin/billing/apps'), {
  // 获取Key状态和详细信息
  getAppStatus: async (id: string): Promise<AppStatusResponse> => {
    return await http.get(`/admin/billing/apps/${id}/status`)
  },

  // 获取用量历史
  getUsageHistory: async (id: string, params?: {
    page?: number
    page_size?: number
    module?: ServiceModule
    start_time?: number
    end_time?: number
  }): Promise<PaginationResponse<UsageLog>> => {
    return await http.get(`/admin/billing/apps/${id}/usage`, {
      params,
    })
  },

  // 获取用量统计
  getUsageStats: async (id: string, period: 'day' | 'week' | 'month' = 'month'): Promise<UsageStats> => {
    return await http.get(`/admin/billing/apps/${id}/stats`, {
      params: {
        period,
      },
    })
  },

  // 获取概览统计
  getOverviewStats: async (): Promise<KeyOverviewStats> => {
    return await http.get('/admin/billing/apps/overview/stats')
  },
})
