import type { UploadFile, UploadResult } from '@billing/common'
import { http } from '@uozi-admin/request'

export const uploadApi = {
  // 上传文件（用于头像等）
  upload: async (file: File, to: string = ''): Promise<UploadFile> => {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('to', to)

    return await http.post('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },

  // 上传临时文件
  uploadTemp: async (file: File): Promise<UploadResult> => {
    const formData = new FormData()
    formData.append('file', file)

    return await http.post('/upload_temp', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },

  // 获取上传文件URL
  getUploadUrl: async (path: string): Promise<{ url: string }> => {
    return await http.get('/upload/url', {
      params: { path },
    })
  },
}
