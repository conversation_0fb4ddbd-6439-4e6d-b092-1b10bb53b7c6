import type { User } from '@billing/common'
import { extendCurdApi, http, useCurdApi } from '@uozi-admin/request'

// 密码重置请求
export interface ResetPasswordRequest {
  password: string
}

// 用户更新请求
export interface UserUpdateRequest extends Pick<User, 'name' | 'email' | 'phone' | 'status' | 'avatar_id'> {
  password?: string
}

export const userApi = extendCurdApi(useCurdApi<User>('/admin/users'), {
  // 重置用户密码
  resetPassword: async (userId: string, password: string): Promise<void> => {
    return await http.post(`/admin/users/${userId}/reset-password`, { password })
  },

  // 生成随机密码并重置
  generateAndResetPassword: async (userId: string): Promise<{ password: string }> => {
    return await http.post(`/admin/users/${userId}/generate-password`)
  },

  // 更新用户状态
  updateStatus: async (userId: string, status: number): Promise<User> => {
    return await http.patch(`/admin/users/${userId}/status`, { status })
  },

  // 更新用户头像
  updateAvatar: async (userId: string, avatarId: string): Promise<User> => {
    return await http.patch(`/admin/users/${userId}/avatar`, { avatar_id: avatarId })
  },

  // 获取用户详细统计信息
  getUserStats: async (userId: string): Promise<{
    app_count: number
    total_usage: number
    total_cost: number
    recharge_count: number
    total_recharge: number
  }> => {
    return await http.get(`/admin/users/${userId}/stats`)
  },

  // 搜索用户
  searchUsers: async (query: string): Promise<{ label: string, value: number, avatar?: string, email?: string }[]> => {
    try {
      const response = await http.get<Array<{
        id: number
        name: string
        email: string
        avatar?: string
      }>>('/admin/users/search', {
        params: { q: query, limit: 20 },
      })

      return response.map(user => ({
        label: user.name,
        value: user.id,
        email: user.email,
        avatar: user.avatar,
      }))
    }
    catch (error) {
      console.error('搜索用户失败:', error)
      return []
    }
  },

})
