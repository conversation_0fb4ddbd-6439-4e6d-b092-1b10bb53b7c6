/**
 * 复制功能 Composable
 * 提供文本复制和状态管理功能
 */

import { TIME_INTERVALS } from '@billing/common'
import { reactive, ref } from 'vue'

/**
 * 复制功能 Hook
 */
export function useCopy() {
  // 复制状态映射
  const copiedMap = reactive<Record<string, boolean>>({})

  /**
   * 复制文本到剪贴板
   * @param text 要复制的文本
   * @param key 可选的键值，用于跟踪复制状态
   */
  async function copyText(text: string, key?: string): Promise<boolean> {
    try {
      await navigator.clipboard.writeText(text)

      // 如果提供了 key，则更新状态
      if (key) {
        copiedMap[key] = true
        setTimeout(() => {
          copiedMap[key] = false
        }, TIME_INTERVALS.COPY_TOAST_DURATION)
      }

      return true
    }
    catch (error) {
      console.error('复制失败:', error)
      return false
    }
  }

  /**
   * 复制 API Key
   * @param key API Key 字符串
   */
  function copyApiKey(key: string) {
    if (!key)
      return

    copyText(key, key)
  }

  /**
   * 检查指定 key 是否已复制
   * @param key 要检查的键值
   */
  function isCopied(key: string): boolean {
    return copiedMap[key] || false
  }

  /**
   * 清除所有复制状态
   */
  function clearCopiedStates() {
    Object.keys(copiedMap).forEach((key) => {
      copiedMap[key] = false
    })
  }

  return {
    copiedMap,
    copyText,
    copyApiKey,
    isCopied,
    clearCopiedStates,
  }
}

/**
 * 单个复制状态 Hook
 * 适用于单个复制操作的场景
 */
export function useSingleCopy() {
  const copied = ref(false)

  /**
   * 复制文本
   * @param text 要复制的文本
   */
  async function copy(text: string): Promise<boolean> {
    try {
      await navigator.clipboard.writeText(text)
      copied.value = true

      setTimeout(() => {
        copied.value = false
      }, TIME_INTERVALS.COPY_TOAST_DURATION)

      return true
    }
    catch (error) {
      console.error('复制失败:', error)
      return false
    }
  }

  return {
    copied,
    copy,
  }
}
