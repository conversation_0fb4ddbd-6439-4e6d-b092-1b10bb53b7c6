<script setup lang="ts">
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarInset,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarProvider,
  SidebarSeparator,
  SidebarTrigger,
} from '@billing/ui'
import {
  Bell,
  ChevronRight,
  ChevronsUpDown,
  Home,
} from 'lucide-vue-next'
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { authApi } from '@/api/auth'
import { menuRoutes } from '@/router'
import { useUserStore } from '@/store/user'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

const userMenuOpen = ref(false)
const expandedMenus = ref<Set<string>>(new Set())

// 切换子菜单展开状态
function toggleSubmenu(menuName: string) {
  if (expandedMenus.value.has(menuName)) {
    expandedMenus.value.delete(menuName)
  }
  else {
    expandedMenus.value.add(menuName)
  }
}

// 自动展开包含当前路由的菜单项
function autoExpandActiveMenu() {
  const currentPath = route.path

  navigation.value.forEach((item) => {
    if (item.children && item.children.length > 0) {
      // 检查是否有子菜单项匹配当前路径
      const hasActiveChild = item.children.some(child =>
        currentPath.startsWith(child.href) || currentPath === child.href,
      )

      if (hasActiveChild) {
        expandedMenus.value.add(item.name)
      }
    }
  })
}

// 组件挂载时和路由变化时自动展开菜单
onMounted(() => {
  autoExpandActiveMenu()
})

watch(route, () => {
  autoExpandActiveMenu()
})

// 从 menuRoutes 直接生成导航菜单
const navigation = computed(() => {
  // 菜单项类型定义
  interface MenuItem {
    label: string
    name: string
    href: string
    icon: any
    children?: MenuItem[]
  }

  // 递归构建菜单项
  function buildMenuItems(routes: any[], parentPath = ''): MenuItem[] {
    const items: MenuItem[] = []

    routes.forEach((route) => {
      if (
        !route.meta?.title
        || !route.meta?.showInMenu
        || !route.meta?.requiresAuth
      ) {
        return
      }

      const fullPath = parentPath + route.path

      // 构建菜单项
      const menuItem: MenuItem = {
        label: route.meta.title,
        name: route.name,
        href: fullPath,
        icon: route.meta.icon,
      }

      // 如果有子路由，递归处理
      if (route.children && route.children.length > 0) {
        const childMenuItems = buildMenuItems(route.children, fullPath === '/' ? '' : fullPath)
        if (childMenuItems.length > 0) {
          menuItem.children = childMenuItems
        }
      }

      items.push(menuItem)
    })

    return items
  }

  return buildMenuItems(menuRoutes)
})

// 面包屑导航
const breadcrumbs = computed(() => {
  // 如果路由meta中自定义了面包屑，优先使用
  if (route.meta?.breadcrumb) {
    return route.meta.breadcrumb
  }

  const pathSegments = route.path.split('/').filter(Boolean)
  const crumbs: { name: string, href: string }[] = []

  // 构建面包屑路径
  let currentPath = ''
  for (let i = 0; i < pathSegments.length; i++) {
    currentPath += `/${pathSegments[i]}`

    // 尝试匹配路由获取标题
    const matchedRoute = router.getRoutes().find((r) => {
      // 处理动态路由参数
      const routePath = r.path.replace(/:\w+/g, '[^/]+')
      const regex = new RegExp(`^${routePath}$`)
      return regex.test(currentPath)
    })

    if (matchedRoute?.meta?.title) {
      crumbs.push({
        name: matchedRoute.meta.title,
        href: currentPath,
      })
    }
  }

  return crumbs
})

// 退出登录
async function handleLogout() {
  try {
    await authApi.logout()
  }
  catch (error) {
    console.error('退出登录失败:', error)
  }
  finally {
    userStore.reset()
    await router.push('/login')
  }
}
</script>

<template>
  <SidebarProvider>
    <Sidebar variant="inset">
      <SidebarHeader>
        <div class="flex items-center gap-2 px-4 py-2">
          <!-- Logo/图标 -->
          <div class="logo-container gpu-accelerated mx-auto w-10 h-10 bg-white rounded-xl flex items-center justify-center transition-all! duration-300 hover:scale-105 shadow-lg shadow-primary/10">
            <img
              src="/logo.png"
              alt="logo"
              class="mt-1.5 transition-transform duration-300"
            >
          </div>
          <div class="grid flex-1 text-left text-sm leading-tight">
            <span class="truncate font-semibold">计费系统管理端</span>
            <span class="truncate text-xs">Billing System For Admin</span>
          </div>
        </div>
      </SidebarHeader>

      <SidebarContent>
        <SidebarMenu>
          <SidebarMenuItem
            v-for="item in navigation"
            :key="item.label"
          >
            <!-- 有子菜单的项目 -->
            <template v-if="item.children && item.children.length > 0">
              <SidebarMenuButton
                class="h-10!"
                :is-active="$route.name === item.name"
                @click="toggleSubmenu(item.name)"
              >
                <component :is="item.icon" />
                <span>{{ item.label }}</span>
                <ChevronRight
                  class="ml-auto transition-transform duration-200"
                  :class="{ 'rotate-90': expandedMenus.has(item.name) }"
                />
              </SidebarMenuButton>

              <SidebarMenuSub v-if="expandedMenus.has(item.name)">
                <SidebarMenuSubItem
                  v-for="subItem in item.children"
                  :key="subItem.label"
                >
                  <SidebarMenuSubButton
                    class="h-10!"
                    as-child
                    :is-active="$route.name === subItem.name"
                  >
                    <RouterLink :to="{ name: subItem.name }">
                      <span>{{ subItem.label }}</span>
                    </RouterLink>
                  </SidebarMenuSubButton>
                </SidebarMenuSubItem>
              </SidebarMenuSub>
            </template>

            <!-- 没有子菜单的项目 -->
            <template v-else>
              <SidebarMenuButton
                class="h-12!"
                as-child
                :is-active="$route.name === item.name"
              >
                <RouterLink :to="{ name: item.name }">
                  <component :is="item.icon" />
                  <span>{{ item.label }}</span>
                </RouterLink>
              </SidebarMenuButton>
            </template>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarContent>

      <SidebarSeparator />

      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <DropdownMenu v-model:open="userMenuOpen">
              <DropdownMenuTrigger as-child>
                <SidebarMenuButton
                  size="lg"
                  class="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                >
                  <Avatar class="h-8 w-8 rounded-lg">
                    <AvatarImage :src="userStore.user?.avatar || ''" />
                    <AvatarFallback class="rounded-lg">
                      {{ (userStore.user?.name || '').charAt(0) || 'U' }}
                    </AvatarFallback>
                  </Avatar>
                  <div class="grid flex-1 text-left text-sm leading-tight">
                    <span class="truncate font-semibold">{{ userStore.user?.name || '用户' }}</span>
                    <span class="truncate text-xs">{{ userStore.user?.email || '' }}</span>
                  </div>
                  <ChevronsUpDown class="w-4 h-4" />
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                class="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
                align="start"
                side="bottom"
                :side-offset="4"
              >
                <DropdownMenuItem>
                  个人资料
                </DropdownMenuItem>
                <DropdownMenuItem>
                  设置
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem @click="handleLogout">
                  退出登录
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>

    <SidebarInset>
      <!-- 顶部栏 -->
      <header class="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
        <div class="flex items-center gap-2 px-4">
          <SidebarTrigger class="-ml-1" />

          <!-- 面包屑导航 -->
          <Breadcrumb>
            <BreadcrumbList class="gap-1!">
              <BreadcrumbItem>
                <BreadcrumbLink as-child>
                  <RouterLink to="/">
                    <Home class="h-4 w-4" />
                  </RouterLink>
                </BreadcrumbLink>
              </BreadcrumbItem>

              <template
                v-for="(breadcrumb, index) in breadcrumbs"
                :key="breadcrumb.name"
              >
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbLink
                    v-if="index < breadcrumbs.length - 1"
                    as-child
                  >
                    <RouterLink :to="breadcrumb.href">
                      {{ breadcrumb.name }}
                    </RouterLink>
                  </BreadcrumbLink>
                  <BreadcrumbPage v-else>
                    {{ breadcrumb.name }}
                  </BreadcrumbPage>
                </BreadcrumbItem>
              </template>
            </BreadcrumbList>
          </Breadcrumb>
        </div>

        <!-- 右侧工具栏 -->
        <div class="ml-auto px-3">
          <Button
            variant="ghost"
            size="sm"
          >
            <Bell class="h-4 w-4" />
          </Button>
        </div>
      </header>

      <!-- 主内容 -->
      <div class="flex flex-1 flex-col gap-4 p-4 pt-0">
        <RouterView />
      </div>
    </SidebarInset>
  </SidebarProvider>
</template>

<style scoped>
/* Logo 微妙浮动动画 */
.logo-container {
  overflow: hidden;
  animation: float 8s cubic-bezier(0.4, 0, 0.2, 1) infinite;
  will-change: transform;
}

/* 高性能优化 */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}
</style>
