import { serviceInterceptor } from '@billing/common'
import { createApp } from 'vue'
import { errorHandler, getToken } from './api/http'
import App from './App.vue'
import { router } from './router'
import { pinia } from './store'
import './style.css'
import './types/router'

createApp(App)
  .use(serviceInterceptor({
    getToken,
    responseErrorHandler: errorHandler,
  }))
  .use(pinia)
  .use(router)
  .mount('#app')
