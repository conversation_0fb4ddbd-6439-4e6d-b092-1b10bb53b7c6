import type { RouteRecordRaw } from 'vue-router'
import { DollarSign } from 'lucide-vue-next'

export const PATH_BILLING = '/billing'
export const PATH_BILLING_APPS = `${PATH_BILLING}/apps`
export const PATH_BILLING_APP_DETAIL = `${PATH_BILLING_APPS}/:id`
export const PATH_BILLING_APP_STATS = `${PATH_BILLING_APP_DETAIL}/stats`
export const PATH_BILLING_PRICING = `${PATH_BILLING}/pricing`
export const PATH_BILLING_RECHARGE = `${PATH_BILLING}/recharge`
export const PATH_BILLING_QUOTA = `${PATH_BILLING}/quota`

export const billingRoutes: RouteRecordRaw[] = [
  {
    path: PATH_BILLING,
    redirect: PATH_BILLING_APPS,
    meta: {
      title: '计费管理',
      requiresAuth: true,
      icon: DollarSign,
      showInMenu: true,
    },
    children: [
      {
        path: PATH_BILLING_APPS,
        name: 'Billing<PERSON><PERSON>',
        component: () => import('@/views/billing/app/index.vue'),
        meta: {
          title: '应用管理',
          requiresAuth: true,
          showInMenu: true,
        },
      },
      {
        path: PATH_BILLING_APP_DETAIL,
        name: 'BillingAppDetail',
        component: () => import('@/views/billing/app/detail.vue'),
        meta: {
          title: '详情',
          requiresAuth: true,
        },
      },
      {
        path: PATH_BILLING_APP_STATS,
        name: 'BillingAppStats',
        component: () => import('@/views/billing/app/stats.vue'),
        meta: {
          title: '用量统计',
          requiresAuth: true,
        },
      },
      {
        path: PATH_BILLING_PRICING,
        name: 'BillingPricing',
        component: () => import('@/views/billing/pricing/index.vue'),
        meta: {
          title: '计费规则',
          requiresAuth: true,
          showInMenu: true,
        },
      },
      {
        path: PATH_BILLING_RECHARGE,
        name: 'BillingRecharge',
        component: () => import('@/views/billing/recharge/index.vue'),
        meta: {
          title: '充值管理',
          requiresAuth: true,
          showInMenu: true,
        },
      },
      {
        path: PATH_BILLING_QUOTA,
        name: 'QuotaPackage',
        component: () => import('@/views/billing/quota/index.vue'),
        meta: {
          title: '资源包管理',
          requiresAuth: true,
          showInMenu: true,
        },
      },
    ],
  },
]
