import type { RouteRecordRaw } from 'vue-router'
import { Settings } from 'lucide-vue-next'

export const PATH_SETTINGS = '/settings'
export const PATH_SETTINGS_GENERAL = `${PATH_SETTINGS}/general`
export const PATH_SETTINGS_AUTH = `${PATH_SETTINGS}/auth`

export const settingsRoutes: RouteRecordRaw[] = [
  {
    path: PATH_SETTINGS,
    redirect: PATH_SETTINGS_GENERAL,
    name: 'Settings',
    meta: {
      title: '系统设置',
      requiresAuth: true,
      icon: Settings,
      showInMenu: true,
    },
    children: [
      {
        path: PATH_SETTINGS_GENERAL,
        name: 'SettingsGeneral',
        component: () => import('@/views/settings/general.vue'),
        meta: {
          title: '通用设置',
          requiresAuth: true,
          showInMenu: true,
        },
      },
      {
        path: PATH_SETTINGS_AUTH,
        name: 'SettingsAuth',
        component: () => import('@/views/settings/auth.vue'),
        meta: {
          title: '认证设置',
          requiresAuth: true,
          showInMenu: true,
        },
      },
    ],
  },
]
