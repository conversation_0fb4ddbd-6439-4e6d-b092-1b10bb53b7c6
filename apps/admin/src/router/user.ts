import type { RouteRecordRaw } from 'vue-router'
import { Users } from 'lucide-vue-next'

export const PATH_USERS = '/users'
export const PATH_USERS_SYSTEM = `${PATH_USERS}/system`
export const PATH_USERS_CLIENT = `${PATH_USERS}/client`
export const PATH_USERS_DETAIL = `${PATH_USERS}/:id`

export const userRoutes: RouteRecordRaw[] = [
  {
    path: PATH_USERS,
    name: 'Users',
    meta: {
      title: '用户管理',
      icon: Users,
      requiresAuth: true,
      showInMenu: true,
    },
    children: [
      {
        path: PATH_USERS_SYSTEM,
        name: 'UsersSystem',
        component: () => import('@/views/users/index.vue'),
        meta: {
          title: '系统用户',
          requiresAuth: true,
          showInMenu: true,
        },
      },
      {
        path: PATH_USERS_CLIENT,
        name: 'UsersClient',
        component: () => import('@/views/client/index.vue'),
        meta: {
          title: '客户管理',
          requiresAuth: true,
          showInMenu: true,
        },
      },
    ],
  },
  {
    path: PATH_USERS_DETAIL,
    name: 'UserDetail',
    component: () => import('@/views/users/detail.vue'),
    meta: {
      title: '用户详情',
      requiresAuth: true,
    },
  },
]
