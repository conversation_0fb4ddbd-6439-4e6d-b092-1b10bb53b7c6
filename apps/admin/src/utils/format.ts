/**
 * 格式化工具函数
 * 提供统一的数据格式化功能
 */

/**
 * 格式化货币金额
 * @param amount 金额数值
 * @param currency 货币类型，默认为 CNY
 * @param options 格式化选项
 */
export function formatCurrency(
  amount: number,
  currency: string = 'CNY',
  options: Intl.NumberFormatOptions = {},
): string {
  const defaultOptions: Intl.NumberFormatOptions = {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 4,
  }

  return new Intl.NumberFormat('zh-CN', {
    ...defaultOptions,
    ...options,
  }).format(amount)
}

/**
 * 格式化日期时间
 * @param dateInput 日期输入（字符串、数字或Date对象）
 * @param options 格式化选项
 */
export function formatDate(
  dateInput: string | number | Date,
  options: Intl.DateTimeFormatOptions = {},
): string {
  if (!dateInput)
    return '-'

  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  }

  const date = typeof dateInput === 'string' || typeof dateInput === 'number'
    ? new Date(dateInput)
    : dateInput

  return new Intl.DateTimeFormat('zh-CN', {
    ...defaultOptions,
    ...options,
  }).format(date)
}

/**
 * 格式化时间戳（简化版本）
 * @param timestamp 时间戳
 */
export function formatTime(timestamp: number): string {
  return new Date(timestamp).toLocaleString('zh-CN')
}

/**
 * 格式化相对时间（多久前）
 * @param dateInput 日期输入
 */
export function formatTimeAgo(dateInput: string | number | Date): string {
  if (!dateInput)
    return '从未'

  const now = new Date()
  const date = typeof dateInput === 'string' || typeof dateInput === 'number'
    ? new Date(dateInput)
    : dateInput

  const diffMs = now.getTime() - date.getTime()
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (diffDays === 0)
    return '今天'
  if (diffDays === 1)
    return '昨天'
  if (diffDays < 7)
    return `${diffDays}天前`
  if (diffDays < 30)
    return `${Math.floor(diffDays / 7)}周前`
  return `${Math.floor(diffDays / 30)}个月前`
}

/**
 * 格式化数字（K/M 格式）
 * @param num 数字
 */
export function formatNumber(num: number): string {
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`
  }
  if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`
  }
  return num.toLocaleString()
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0)
    return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return `${Number.parseFloat((bytes / (k ** i)).toFixed(2))} ${sizes[i]}`
}

/**
 * 格式化使用量（带单位）
 * @param usage 使用量
 * @param unit 单位
 */
export function formatUsageWithUnit(usage: number, unit: string): string {
  let unitStr = unit
  switch (unit) {
    case 'token':
      unitStr = 'tokens'
      break
    case 'character':
      unitStr = '字符'
      break
    case 'seconds':
      unitStr = '秒'
      break
  }

  let usageStr = usage.toLocaleString()

  if (unit === 'token' || unit === 'character') {
    // 对于tokens和character，使用K/M格式
    if (usage >= 1000000) {
      usageStr = `${(usage / 1000000).toFixed(1)}M`
    }
    else if (usage >= 1000) {
      usageStr = `${(usage / 1000).toFixed(1)}K`
    }
    usageStr = `${usageStr} ${unitStr}`
  }
  else if (unit === 'second') {
    // 对于秒数，格式化为时分秒
    if (usage >= 3600) {
      const hours = Math.floor(usage / 3600)
      const minutes = Math.floor((usage % 3600) / 60)
      const seconds = usage % 60
      usageStr = `${hours}小时${minutes}分${seconds}秒`
    }
    else if (usage >= 60) {
      const minutes = Math.floor(usage / 60)
      const seconds = usage % 60
      usageStr = `${minutes}分${seconds}秒`
    }
    else {
      usageStr = `${usageStr}s`
    }
  }

  return usageStr
}

/**
 * 格式化过期时间
 * @param timestamp 时间戳
 */
export function formatExpiryTime(timestamp?: number): string {
  if (!timestamp)
    return '永不过期'

  const date = new Date(timestamp)
  const now = new Date()

  if (date.getTime() < now.getTime()) {
    return '已过期'
  }

  return date.toLocaleString('zh-CN')
}
