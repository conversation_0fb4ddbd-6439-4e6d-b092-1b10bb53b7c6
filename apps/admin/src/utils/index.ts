/**
 * 工具函数统一导出
 */

// 业务工具函数
export {
  copyToClipboard,
  getAvailabilityColor,
  getBillingTypeColor,
  getFileTypeBadgeVariant,
  getModuleColorClass,
  getQuotaStatusVariant,
  getRechargeStatusText,
  getRechargeStatusVariant,
  getRechargeTypeColor,
  getStatusColor,
  getTrendColor,
  getTrendIcon,
  getUsageRate,
  isQuotaExpired,
  maskApiKey,
} from './business'

// 格式化函数
export {
  formatCurrency,
  formatDate,
  formatExpiryTime,
  formatFileSize,
  formatNumber,
  formatTime,
  formatTimeAgo,
  formatUsageWithUnit,
} from './format'
