<script setup lang="ts">
import type { AppStatusResponse } from '@/api/app'
import { APP_STATUS, APP_STATUS_TEXT } from '@billing/common'
import {
  Badge,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Progress,
} from '@billing/ui'
import { AlertCircle, CheckCircle, Package, User, Wallet, XCircle } from 'lucide-vue-next'
import { computed } from 'vue'
import { formatCurrency, formatTime, getAvailabilityColor, getStatusColor } from '@/utils'

interface Props {
  keyInfo: AppStatusResponse
  quotaStats: Array<{
    key: string
    module: string
    modelName: string
    displayName: string
    total: number
    used: number
    available: number
    quotas: any[]
  }> | null
}

const props = defineProps<Props>()

// 计算属性：计费方式描述
const billingModeDescription = computed(() => {
  if (!props.keyInfo)
    return ''

  const hasQuotas = props.keyInfo.quotas && props.keyInfo.quotas.length > 0
  const hasBalance = props.keyInfo.user && props.keyInfo.user.balance > 0

  if (hasQuotas && hasBalance) {
    return '混合计费：优先使用资源包，资源包用完后扣除用户余额'
  }
  else if (hasQuotas) {
    return '资源包计费：仅使用配置的资源包'
  }
  else if (hasBalance) {
    return '余额计费：直接扣除用户余额'
  }
  else {
    return '无可用计费方式：请添加资源包或为用户充值'
  }
})
</script>

<template>
  <Card>
    <CardHeader>
      <CardTitle class="flex items-center gap-2">
        <User class="w-5 h-5" />
        Key信息
      </CardTitle>
    </CardHeader>
    <CardContent class="space-y-4">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div>
          <div class="text-sm font-medium text-gray-500">
            状态
          </div>
          <Badge
            :variant="getStatusColor(keyInfo.status)"
            class="mt-1"
          >
            <CheckCircle
              v-if="keyInfo.status === APP_STATUS.OK"
              class="w-3 h-3"
            />
            <XCircle
              v-else
              class="w-3 h-3"
            />
            {{ APP_STATUS_TEXT[keyInfo.status] }}
          </Badge>
        </div>
        <div>
          <div class="text-sm font-medium text-gray-500">
            可用性
          </div>
          <Badge
            :variant="getAvailabilityColor(keyInfo.available)"
            class="mt-1"
          >
            <CheckCircle
              v-if="keyInfo.available"
              class="w-3 h-3"
            />
            <AlertCircle
              v-else
              class="w-3 h-3"
            />
            {{ keyInfo.available ? '可用' : '不可用' }}
          </Badge>
        </div>
        <div>
          <div class="text-sm font-medium text-gray-500">
            创建时间
          </div>
          <div class="mt-1 text-sm">
            {{ formatTime(keyInfo.created_at) }}
          </div>
        </div>
        <div>
          <div class="text-sm font-medium text-gray-500">
            更新时间
          </div>
          <div class="mt-1 text-sm">
            {{ formatTime(keyInfo.updated_at) }}
          </div>
        </div>
      </div>

      <!-- 关联用户信息 -->
      <div
        v-if="keyInfo.user"
        class="border-t pt-4"
      >
        <div class="text-sm font-medium text-gray-500 mb-2">
          关联用户信息
        </div>
        <div class="flex items-center gap-3">
          <div class="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
            <User class="w-5 h-5 text-gray-500" />
          </div>
          <div class="flex-1">
            <div class="font-medium">
              {{ keyInfo.user.name }}
            </div>
            <div class="text-sm text-gray-500">
              {{ keyInfo.user.email }}
            </div>
            <div class="flex items-center gap-4 mt-2">
              <div class="flex items-center gap-1">
                <Wallet class="w-4 h-4 text-green-600" />
                <span class="text-sm font-medium text-green-600">
                  余额: {{ formatCurrency(keyInfo.user.balance, 'CNY', { maximumFractionDigits: 2 }) }}
                </span>
              </div>
              <div class="flex items-center gap-1">
                <Package class="w-4 h-4 text-blue-600" />
                <span class="text-sm text-blue-600">
                  {{ quotaStats ? quotaStats.length : keyInfo.quotas?.length ?? 0 }} 个资源组
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 资源包概览 -->
      <div
        v-if="quotaStats && quotaStats.length > 0"
        class="border-t pt-4"
      >
        <div class="text-sm font-medium text-gray-500 mb-3">
          资源包概览
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div
            v-for="stat in quotaStats"
            :key="stat.key"
            class="p-3 bg-gray-50 rounded-lg"
          >
            <div class="text-sm font-medium mb-2">
              {{ stat.displayName }}
            </div>
            <div class="space-y-1">
              <div class="flex justify-between text-xs">
                <span>总配额</span>
                <span>{{ stat.total.toLocaleString() }}</span>
              </div>
              <div class="flex justify-between text-xs">
                <span>已使用</span>
                <span>{{ stat.used.toLocaleString() }}</span>
              </div>
              <div class="flex justify-between text-xs">
                <span>可用量</span>
                <span>{{ stat.available.toLocaleString() }}</span>
              </div>
              <Progress
                :model-value="stat.total > 0 ? (stat.used / stat.total) * 100 : 0"
                class="h-1 mt-2"
              />
              <div class="flex justify-between text-xs text-gray-500 mt-1">
                <span>使用率</span>
                <span>{{ stat.total > 0 ? ((stat.used / stat.total) * 100).toFixed(2) : 0 }}%</span>
              </div>
              <div
                v-if="stat.quotas.length > 1"
                class="text-xs text-blue-600 mt-1"
              >
                {{ stat.quotas.length }} 个资源包
              </div>
            </div>
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
</template>
