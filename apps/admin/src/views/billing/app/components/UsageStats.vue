<script setup lang="ts">
import type { AcceptableValue } from 'reka-ui'
import type { UsageStats } from '@/api/app'
import { MODULE_NAMES } from '@billing/common'
import {
  Badge,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@billing/ui'
import { BarChart3, Package } from 'lucide-vue-next'
import { computed } from 'vue'
import {
  formatCurrency,
  formatUsageWithUnit,
  getModuleColorClass,
} from '@/utils'

interface Props {
  usageStats: UsageStats | null
  selectedPeriod: 'day' | 'week' | 'month'
}

interface Emits {
  (e: 'update:selectedPeriod', value: 'day' | 'week' | 'month'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const periodText = computed(() => {
  switch (props.selectedPeriod) {
    case 'day': return '今日'
    case 'week': return '本周'
    case 'month': return '本月'
    default: return '本月'
  }
})

function handlePeriodChange(value: AcceptableValue) {
  emit('update:selectedPeriod', value as 'day' | 'week' | 'month')
}
</script>

<template>
  <Card>
    <CardHeader>
      <div class="flex items-center justify-between">
        <CardTitle class="flex items-center gap-2">
          <BarChart3 class="w-5 h-5" />
          用量统计
        </CardTitle>
        <Select
          :model-value="selectedPeriod"
          @update:model-value="handlePeriodChange"
        >
          <SelectTrigger class="w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="day">
              今日
            </SelectItem>
            <SelectItem value="week">
              本周
            </SelectItem>
            <SelectItem value="month">
              本月
            </SelectItem>
          </SelectContent>
        </Select>
      </div>
    </CardHeader>
    <CardContent>
      <div
        v-if="usageStats"
        class="space-y-4"
      >
        <!-- 总计 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="text-center p-4 bg-green-50 rounded-lg">
            <div class="text-2xl font-bold text-green-600">
              {{ formatCurrency(usageStats.total_cost) }}
            </div>
            <div class="text-sm text-green-600">
              总费用
            </div>
          </div>
          <div class="text-center p-4 bg-purple-50 rounded-lg">
            <div class="text-2xl font-bold text-purple-600">
              {{ usageStats.total_count.toLocaleString() }}
            </div>
            <div class="text-sm text-purple-600">
              调用次数
            </div>
          </div>
        </div>

        <!-- 按模块详细统计 -->
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <h4 class="font-medium">
              按模块详细统计
            </h4>
            <Badge
              variant="secondary"
              class="text-xs"
            >
              {{ periodText }}
            </Badge>
          </div>

          <div
            v-if="usageStats.by_module.length === 0"
            class="text-center py-8 text-gray-500"
          >
            <Package class="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p class="font-medium">
              暂无使用记录
            </p>
            <p class="text-sm">
              当前时间段内该Key暂无使用记录
            </p>
          </div>

          <!-- 模块统计卡片网格 -->
          <div
            v-else
            class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
          >
            <div
              v-for="moduleStats in usageStats.by_module"
              :key="moduleStats.module"
              class="bg-white border rounded-lg p-6 hover:shadow-md transition-shadow"
            >
              <!-- 模块头部 -->
              <div class="flex items-center justify-between mb-4">
                <div class="flex items-center gap-2">
                  <div
                    class="w-3 h-3 rounded-full"
                    :class="getModuleColorClass(moduleStats.module)"
                  />
                  <Badge
                    variant="outline"
                    class="text-sm"
                  >
                    {{ MODULE_NAMES[moduleStats.module] }}
                  </Badge>
                </div>
                <Badge
                  :variant="moduleStats.count > 0 ? 'default' : 'secondary'"
                  class="text-xs"
                >
                  {{ moduleStats.count > 0 ? '活跃' : '无使用' }}
                </Badge>
              </div>

              <!-- 使用量统计 -->
              <div class="space-y-3">
                <div>
                  <div class="flex items-center justify-between text-sm mb-1">
                    <span class="text-gray-600">使用量</span>
                    <span class="font-medium">{{ formatUsageWithUnit(moduleStats.total_usage, moduleStats.unit) }}</span>
                  </div>
                </div>

                <div>
                  <div class="flex items-center justify-between text-sm mb-1">
                    <span class="text-gray-600">调用次数</span>
                    <span class="font-medium">{{ moduleStats.count.toLocaleString() }}</span>
                  </div>
                </div>

                <div>
                  <div class="flex items-center justify-between text-sm mb-1">
                    <span class="text-gray-600">总费用</span>
                    <span class="font-medium text-green-600">{{ formatCurrency(moduleStats.total_cost) }}</span>
                  </div>
                </div>

                <!-- 平均每次调用成本 -->
                <div v-if="moduleStats.count > 0">
                  <div class="flex items-center justify-between text-sm mb-1">
                    <span class="text-gray-600">单次成本</span>
                    <span class="font-medium text-blue-600">
                      {{ formatCurrency(moduleStats.total_cost / moduleStats.count) }}
                    </span>
                  </div>
                </div>

                <!-- 使用效率指标 -->
                <div
                  v-if="moduleStats.count > 0"
                  class="pt-2 border-t"
                >
                  <div class="flex items-center justify-between text-xs text-gray-500">
                    <span>平均每次用量</span>
                    <span>{{ formatUsageWithUnit(Math.round(moduleStats.total_usage / moduleStats.count), moduleStats.unit) }}</span>
                  </div>
                </div>
              </div>

              <!-- 在总统计中的占比 -->
              <div class="mt-4 pt-3 border-t">
                <div class="text-xs text-gray-500 mb-2">
                  在总费用中占比
                </div>
                <div class="flex items-center gap-2">
                  <div class="flex-1 bg-gray-200 rounded-full h-2">
                    <div
                      class="h-2 rounded-full transition-all duration-300"
                      :class="getModuleColorClass(moduleStats.module, true)"
                      :style="{ width: `${Math.min((moduleStats.total_cost / (usageStats.total_cost || 1)) * 100, 100)}%` }"
                    />
                  </div>
                  <span class="text-xs font-medium text-gray-600">
                    {{ Math.round((moduleStats.total_cost / (usageStats.total_cost || 1)) * 100) }}%
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        v-else
        class="text-center py-8 text-gray-500"
      >
        暂无统计数据
      </div>
    </CardContent>
  </Card>
</template>
