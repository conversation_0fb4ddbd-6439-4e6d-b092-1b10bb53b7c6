<script setup lang="ts">
import { <PERSON><PERSON>, <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@billing/ui'
import { ArrowLeft } from 'lucide-vue-next'
import { ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()
const keyInfo = ref(route.params.key as string)

function goBack() {
  router.push(`/billing/keys/${keyInfo.value}`)
}
</script>

<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-4">
        <Button
          variant="ghost"
          @click="goBack"
        >
          <ArrowLeft class="w-4 h-4 mr-2" />
          返回
        </Button>
        <div>
          <h1 class="text-2xl font-semibold text-gray-900">
            用量统计
          </h1>
          <p class="mt-1 text-sm text-gray-500">
            API Key: {{ keyInfo }}
          </p>
        </div>
      </div>
    </div>

    <Card>
      <CardHeader>
        <CardTitle>统计图表</CardTitle>
      </CardHeader>
      <CardContent>
        <p class="text-gray-500">
          用量统计页面正在开发中...
        </p>
      </CardContent>
    </Card>
  </div>
</template>
