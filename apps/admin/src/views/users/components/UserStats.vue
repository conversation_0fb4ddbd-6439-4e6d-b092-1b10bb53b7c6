<script setup lang="ts">
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>nt,
  Card<PERSON><PERSON>er,
  CardTitle,
} from '@billing/ui'
import {
  CreditCard,
  Loader2,
} from 'lucide-vue-next'
import { formatCurrency } from '@/utils/format'

interface UserStats {
  app_count: number
  total_usage: number
  total_cost: number
  recharge_count: number
  total_recharge: number
}

interface Props {
  userStats: UserStats | null
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
})
</script>

<template>
  <Card>
    <CardHeader>
      <CardTitle class="flex items-center gap-2">
        <CreditCard class="w-5 h-5" />
        使用统计
      </CardTitle>
    </CardHeader>
    <CardContent>
      <div
        v-if="loading || !userStats"
        class="text-center py-8 text-gray-500"
      >
        <Loader2 class="w-8 h-8 mx-auto mb-4 animate-spin" />
        <p>加载统计数据中...</p>
      </div>
      <div
        v-else
        class="grid grid-cols-2 md:grid-cols-3 gap-4"
      >
        <div class="text-center p-4 border rounded-lg">
          <div class="text-2xl font-bold text-blue-600">
            {{ userStats.app_count }}
          </div>
          <div class="text-sm text-gray-500">
            应用数量
          </div>
        </div>
        <div class="text-center p-4 border rounded-lg">
          <div class="text-2xl font-bold text-green-600">
            {{ formatCurrency(userStats.total_cost) }}
          </div>
          <div class="text-sm text-gray-500">
            总消费
          </div>
        </div>
        <div class="text-center p-4 border rounded-lg">
          <div class="text-2xl font-bold text-purple-600">
            {{ formatCurrency(userStats.total_recharge) }}
          </div>
          <div class="text-sm text-gray-500">
            总充值
          </div>
        </div>
        <div class="text-center p-4 border rounded-lg">
          <div class="text-2xl font-bold text-orange-600">
            {{ userStats.total_usage.toLocaleString() }}
          </div>
          <div class="text-sm text-gray-500">
            总使用量
          </div>
        </div>
        <div class="text-center p-4 border rounded-lg">
          <div class="text-2xl font-bold text-indigo-600">
            {{ userStats.recharge_count }}
          </div>
          <div class="text-sm text-gray-500">
            充值次数
          </div>
        </div>
        <div class="text-center p-4 border rounded-lg">
          <div class="text-2xl font-bold text-pink-600">
            {{ userStats.total_recharge > 0 ? formatCurrency(userStats.total_recharge - userStats.total_cost) : formatCurrency(0) }}
          </div>
          <div class="text-sm text-gray-500">
            余额
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
</template>
