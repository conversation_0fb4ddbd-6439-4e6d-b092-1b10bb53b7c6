<script setup lang="ts">
import type { User } from '@billing/common'
import { USER_STATUS } from '@billing/common'
import {
  Badge,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@billing/ui'
import { computed } from 'vue'
import { formatCurrency, formatDate, formatTimeAgo } from '@/utils/format'

interface Props {
  user: User | null
}

const props = defineProps<Props>()

// 计算属性
const statusBadgeVariant = computed(() => {
  return props.user?.status === 'active' ? 'default' : 'secondary'
})

const statusText = computed(() => {
  return props.user?.status === USER_STATUS.ACTIVE ? '激活' : '禁用'
})
</script>

<template>
  <Card>
    <CardHeader>
      <CardTitle>账户状态</CardTitle>
    </CardHeader>
    <CardContent class="space-y-4">
      <div class="flex justify-between items-center">
        <span class="text-sm text-gray-500">当前状态</span>
        <Badge :variant="statusBadgeVariant">
          {{ statusText }}
        </Badge>
      </div>
      <div class="flex justify-between items-center">
        <span class="text-sm text-gray-500">账户余额</span>
        <span
          class="text-sm font-medium"
          :class="(user?.balance || 0) > 0 ? 'text-green-600' : 'text-gray-500'"
        >
          {{ user ? formatCurrency(user.balance || 0) : '-' }}
        </span>
      </div>
      <div class="flex justify-between items-center">
        <span class="text-sm text-gray-500">最近活跃</span>
        <span class="text-sm font-medium">
          {{ user ? formatTimeAgo(user.last_active) : '-' }}
        </span>
      </div>
      <div class="flex justify-between items-center">
        <span class="text-sm text-gray-500">注册时间</span>
        <span class="text-sm font-medium">
          {{ user ? formatDate(user.created_at) : '-' }}
        </span>
      </div>
    </CardContent>
  </Card>
</template>
