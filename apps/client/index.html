<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/logo.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>柚子 AI</title>
    <script type="module">
      if (import.meta.hot?.on) {
        import.meta.hot.on('vite:error', (error) => {
          if (error.err) {
            console.error([error.err.message, error.err.frame].filter(Boolean).join('\n'));
          }
        });
      }
    </script>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
