{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "check": "vue-tsc -b", "lint": "eslint . --ext .ts,.vue", "lint:fix": "eslint . --ext .ts,.vue --fix"}, "dependencies": {"@billing/common": "workspace:^", "@billing/ui": "workspace:^", "@pinia/nuxt": "^0.11.2", "chart.js": "^4.5.0", "vue-chartjs": "^5.3.2"}, "devDependencies": {"@vue/tsconfig": "^0.7.0", "unplugin-vue-dev-locator": "^1.0.0", "vite-plugin-trae-solo-badge": "^1.0.0"}}