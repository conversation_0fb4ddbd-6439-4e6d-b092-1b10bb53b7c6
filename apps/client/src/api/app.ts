import type { App } from '@billing/common'
import { extendCurdApi, http, useCurdApi } from '@uozi-admin/request'

export interface CreateAppRequest extends Pick<
  App,
  'name'
  | 'comment'
> { }

export interface UpdateAppRequest extends Partial<Pick<App, 'name' | 'comment'>> { }

export const appApi = extendCurdApi(useCurdApi<App>('/client/apps'), {
  // 重新生成API Key
  regenerateApiKey: (appID: string): Promise<App> => {
    return http.post(`/client/apps/${appID}/regenerate`)
  },
})
