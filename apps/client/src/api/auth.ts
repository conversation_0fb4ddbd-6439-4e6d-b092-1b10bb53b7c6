import type { LoginRequest, LoginResponse } from '@billing/common'
import { http } from '@uozi-admin/request'

export interface RegisterRequest {
  username: string
  email: string
  password: string
}

export const authApi = {
  login: (data: LoginRequest): Promise<LoginResponse> => {
    return http.post('/login', {
      ...data,
      source: 'client',
    })
  },
  logout: (): Promise<void> => {
    return http.delete('/logout')
  },
  register: (data: RegisterRequest): Promise<LoginResponse> => {
    return http.post('/register', data)
  },
}
