import type { PaginationResponse } from '@billing/common'
import type {
  BillingPeriod,
  BillingQuery,
  BillingStats,
  ServiceUsage,
  UsageTrend,
} from '@/types/billing'
import { http } from '@uozi-admin/request'

export const billingApi = {
  // 获取计费统计概览
  getBillingStats: (query: BillingQuery): Promise<BillingStats> => {
    return http.get('/client/billing/stats', { params: query })
  },

  // 获取使用趋势数据
  getUsageTrends: (query: BillingQuery): Promise<UsageTrend[]> => {
    return http.get('/client/billing/trends', { params: query })
  },

  // 获取服务使用分布
  getServiceUsage: (query: BillingQuery): Promise<ServiceUsage[]> => {
    return http.get('/client/billing/services', { params: query })
  },

  // 获取计费周期数据
  getBillingPeriods: (query: BillingQuery): Promise<PaginationResponse<BillingPeriod>> => {
    return http.get('/client/billing/periods', { params: query })
  },

  // 获取按模块统计数据
  getModuleStats: (query: BillingQuery): Promise<{
    llm: { calls: number, tokens: number, cost: number, avg_cost_per_call: number }
    tts: { calls: number, tokens: number, cost: number, avg_cost_per_call: number }
    asr: { calls: number, tokens: number, cost: number, avg_cost_per_call: number }
  }> => {
    return http.get('/client/billing/module-stats', { params: query })
  },

  // 获取按模块趋势数据
  getModuleTrends: (query: BillingQuery): Promise<{
    labels: string[]
    datasets: Array<{
      label: string
      data: number[]
    }>
  }> => {
    return http.get('/client/billing/module-trends', { params: query })
  },

  // 获取应用的计费数据
  getApplicationBilling: (appID: string, query: BillingQuery): Promise<{
    stats: BillingStats
    trends: UsageTrend[]
    services: ServiceUsage[]
  }> => {
    return http.get(`/client/applications/${appID}/billing`, { params: query })
  },

  // 导出计费报告
  exportBillingReport: (query: BillingQuery): Promise<Blob> => {
    return http.get('/client/billing/export', {
      params: query,
      responseType: 'blob',
    })
  },
}
