import { storeToRefs } from 'pinia'
import { useRouter } from 'vue-router'
import { PATH_LOGIN } from '@/router/auth'
import { useUserStore } from '@/store'

export interface ModelBase {
  id: string
  created_at: string
  updated_at: string
}

const router = useRouter()

export function getToken() {
  const user = useUserStore()
  const { token } = storeToRefs(user)
  return token.value
}

export async function errorHandler(error: any) {
  const user = useUserStore()

  switch (error.response.status) {
    case 401:
    case 403:
      user.reset()
      await router.push(PATH_LOGIN)
      break
  }
}
