import { computed, ref } from 'vue'

type Theme = 'light' | 'dark' | 'system'

const theme = ref<Theme>('system')

export function useTheme() {
  const setTheme = (newTheme: Theme) => {
    theme.value = newTheme

    if (newTheme === 'system') {
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
      document.documentElement.classList.toggle('dark', systemTheme === 'dark')
    }
    else {
      document.documentElement.classList.toggle('dark', newTheme === 'dark')
    }

    localStorage.setItem('theme', newTheme)
  }

  const initTheme = () => {
    const savedTheme = localStorage.getItem('theme') as Theme || 'system'
    setTheme(savedTheme)
  }

  const toggleTheme = () => {
    const newTheme = theme.value === 'light' ? 'dark' : 'light'
    setTheme(newTheme)
  }

  const getCurrentTheme = () => {
    if (theme.value === 'system') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
    }
    return theme.value
  }

  return {
    theme,
    isDark: computed(() => getCurrentTheme() === 'dark'),
    setTheme,
    toggleTheme,
    initTheme,
  }
}
