<script setup lang="ts">
import { ChevronDown, Github, LogOut, Menu, User } from 'lucide-vue-next'
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { toast } from 'vue-sonner'
import { authApi } from '@/api'
import { useUserStore } from '@/store'

const router = useRouter()

// 导航菜单
const navigation = [
  { name: '服务', href: '#services' },
  { name: '定价', href: '#pricing' },
  { name: '文档', href: '#docs' },
  // { name: '关于', href: '#about' },
]

// 移动端菜单状态
const showMobileMenu = ref(false)
const showUserMenu = ref(false)
const userMenuRef = ref<HTMLElement>()

const userStore = useUserStore()

// 切换移动端菜单
function toggleMobileMenu() {
  showMobileMenu.value = !showMobileMenu.value
}

// 切换用户菜单
function toggleUserMenu() {
  showUserMenu.value = !showUserMenu.value
}

// 处理登出
async function handleLogout() {
  try {
    await authApi.logout()
    toast.success('已成功退出登录')
    router.push('/auth/login')
  }
  catch (error) {
    toast.error('退出登录失败')
  }
}
</script>

<template>
  <div class="min-h-screen bg-white">
    <!-- 顶部导航 -->
    <header class="bg-white/95 backdrop-blur-sm border-b sticky top-0 z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Logo -->
          <div class="flex items-center">
            <RouterLink
              to="/"
              class="flex items-center space-x-2"
            >
              <img
                src="/logo.png"
                alt="Logo"
                class="w-14 h-14 mt-2 rounded-lg"
              >
              <span class="text-xl font-semibold text-gray-900">柚子 AI</span>
            </RouterLink>
          </div>

          <!-- 导航菜单 -->
          <nav class="hidden md:flex space-x-8">
            <a
              v-for="item in navigation"
              :key="item.name"
              :href="item.href"
              class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors"
            >
              {{ item.name }}
            </a>
          </nav>

          <!-- 登录注册按钮 -->
          <div class="hidden md:flex items-center space-x-2">
            <template v-if="!userStore.isLoggedIn">
              <RouterLink
                to="/auth/login"
                class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors"
              >
                登录
              </RouterLink>
              <RouterLink
                to="/auth/register"
                class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors"
              >
                免费注册
              </RouterLink>
            </template>
            <template v-else>
              <!-- 通知 -->
              <RouterLink
                to="/dashboard"
                class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors"
              >
                控制台
              </RouterLink>

              <!-- 用户下拉菜单 -->
              <div
                ref="userMenuRef"
                class="relative"
              >
                <button
                  class="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
                  @click="toggleUserMenu"
                >
                  <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                    <User class="w-4 h-4" />
                  </div>
                  <span class="hidden md:block text-sm font-medium">{{ userStore.user?.name || '用户' }}</span>
                  <ChevronDown class="w-4 h-4" />
                </button>

                <!-- 下拉菜单 -->
                <div
                  v-show="showUserMenu"
                  class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border"
                >
                  <!-- <RouterLink
                    to="/profile"
                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                  >
                    <Settings class="w-4 h-4 inline-block mr-2" />
                    个人设置
                  </RouterLink> -->
                  <button
                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                    @click="handleLogout"
                  >
                    <LogOut class="w-4 h-4 inline-block mr-2" />
                    退出登录
                  </button>
                </div>
              </div>
            </template>
          </div>

          <!-- 移动端菜单按钮 -->
          <button
            class="md:hidden text-gray-600 hover:text-gray-900 transition-colors"
            @click="toggleMobileMenu"
          >
            <Menu class="w-5 h-5" />
          </button>
        </div>
      </div>

      <!-- 移动端导航 -->
      <div
        v-show="showMobileMenu"
        class="md:hidden border-t bg-white"
      >
        <div class="px-2 pt-2 pb-3 space-y-1">
          <a
            v-for="item in navigation"
            :key="item.name"
            :href="item.href"
            class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-colors"
            @click="showMobileMenu = false"
          >
            {{ item.name }}
          </a>
          <div class="border-t pt-2 mt-2">
            <RouterLink
              to="/auth/login"
              class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-colors"
              @click="showMobileMenu = false"
            >
              登录
            </RouterLink>
            <RouterLink
              to="/auth/register"
              class="block px-3 py-2 rounded-md text-base font-medium text-blue-600 hover:text-blue-700 hover:bg-blue-50 transition-colors"
              @click="showMobileMenu = false"
            >
              免费注册
            </RouterLink>
          </div>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main>
      <slot />
    </main>

    <!-- 页脚 -->
    <footer class="bg-gray-900 text-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
          <!-- 公司信息 -->
          <div class="col-span-1 md:col-span-2">
            <div class="flex items-center space-x-2 mb-4">
              <img
                src="/uozi.png"
                alt="Logo"
                class="w-10 h-10 rounded-lg"
              >
              <span class="text-xl font-semibold">柚子星云</span>
            </div>
            <p class="text-gray-400 mb-4 max-w-md">
              为开发者提供专业的 AI 服务解决方案，支持 LLM、TTS、ASR 等多种 AI 服务，提供透明的使用统计和费用管理。
            </p>
            <div class="flex space-x-4">
              <a
                href="https://github.com/uozi-tech"
                class="text-gray-400 hover:text-white transition-colors"
              >
                <Github class="w-5 h-5" />
              </a>
            </div>
          </div>

          <!-- 产品 -->
          <div>
            <h3 class="text-sm font-semibold text-white uppercase tracking-wider mb-4">
              产品
            </h3>
            <ul class="space-y-2">
              <li>
                <a
                  href="#services"
                  class="text-gray-400 hover:text-white transition-colors"
                >AI 服务</a>
              </li>
              <li>
                <a
                  href="#pricing"
                  class="text-gray-400 hover:text-white transition-colors"
                >定价</a>
              </li>
              <li>
                <a
                  href="#docs"
                  class="text-gray-400 hover:text-white transition-colors"
                >API文档</a>
              </li>
            </ul>
          </div>

          <!-- 支持 -->
          <div>
            <h3 class="text-sm font-semibold text-white uppercase tracking-wider mb-4">
              支持
            </h3>
            <ul class="space-y-2">
              <li>
                <a
                  href="#"
                  class="text-gray-400 hover:text-white transition-colors"
                >帮助中心</a>
              </li>
              <li>
                <a
                  href="#"
                  class="text-gray-400 hover:text-white transition-colors"
                >联系我们</a>
              </li>
              <li>
                <a
                  href="#"
                  class="text-gray-400 hover:text-white transition-colors"
                >状态页面</a>
              </li>
            </ul>
          </div>
        </div>

        <div class="border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p class="text-gray-400 text-sm">
            &copy; 2025 柚子星云（深圳）科技有限公司. 保留所有权利.
          </p>
          <div class="flex space-x-6 mt-4 md:mt-0">
            <a
              href="#"
              class="text-gray-400 hover:text-white text-sm transition-colors"
            >隐私政策</a>
            <a
              href="#"
              class="text-gray-400 hover:text-white text-sm transition-colors"
            >服务条款</a>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>
