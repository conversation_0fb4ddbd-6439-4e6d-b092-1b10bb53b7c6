import { serviceInterceptor } from '@billing/common'
import { createApp } from 'vue'
import { errorHandler, getToken } from '@/api/http'
import { pinia } from '@/store'
import App from './App.vue'
import router from './router'
import './style.css'

// 创建Vue应用实例
const app = createApp(App)

app.use(serviceInterceptor({
  getToken,
  responseErrorHandler: errorHandler,
}))
// 使用Pinia状态管理
app.use(pinia)
// 使用路由
app.use(router)

// 挂载应用
app.mount('#app')
