import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/store'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'landing',
      component: () => import('@/views/landing.vue'),
    },
    {
      path: '/auth/login',
      name: 'login',
      component: () => import('@/views/auth/login.vue'),
      meta: { requiresGuest: true },
    },
    {
      path: '/auth/register',
      name: 'register',
      component: () => import('@/views/auth/register.vue'),
      meta: { requiresGuest: true },
    },
    {
      path: '/dashboard',
      name: 'dashboard',
      component: () => import('@/views/dashboard.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/applications',
      name: 'applications',
      component: () => import('@/views/apps.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/billing',
      name: 'billing',
      component: () => import('@/views/billing.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/applications/create',
      name: 'create-application',
      component: () => import('@/views/apps.vue'),
      meta: { requiresAuth: true },
    },
    // 重定向路由
    {
      path: '/home',
      redirect: '/dashboard',
    },
    // 404 页面
    {
      path: '/:pathMatch(.*)*',
      name: 'not-found',
      redirect: '/',
    },
  ],
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()

  // 检查是否需要认证
  if (to.meta.requiresAuth && !userStore.isLoggedIn) {
    next('/auth/login')
    return
  }

  // 检查是否需要游客状态（已登录用户不能访问登录/注册页面）
  if (to.meta.requiresGuest && userStore.isLoggedIn) {
    next('/dashboard')
    return
  }

  next()
})

export default router
