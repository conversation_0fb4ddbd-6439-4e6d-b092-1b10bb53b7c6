import type { ModelBase } from '@billing/common'

export interface ModuleUsage {
  llm: number
  tts: number
  asr: number
}

export interface BillingStats {
  total_calls: number
  total_usage: ModuleUsage
  total_cost: number
  monthly_cost: number
  daily_average_cost: number
  monthly_usage: ModuleUsage
  monthly_cost_growth: number
  app_count: number
  today_cost: number
  today_cost_growth: number
  monthly_calls: number
  avg_cost_per_call: number
}

export interface UsageTrend {
  date: string
  cost: number
  usage: number
  llm_usage: number
  tts_usage: number
  asr_usage: number
}

export interface ServiceUsage {
  labels: string[]
  datasets: {
    data: number[]
    background_color: string[]
    border_width: number
  }[]
}

export interface BillingPeriod extends ModelBase {
  module: string
  usage: number
  calls: number
  cost: number
}

export interface BillingQuery {
  period: 'today' | '7d' | '30d' | '90d' | 'monthly' | 'custom'
  start_date?: string
  end_date?: string
  app_id?: string
}
