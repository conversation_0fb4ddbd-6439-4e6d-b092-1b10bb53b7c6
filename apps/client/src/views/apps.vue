<script setup lang="ts">
import type { App } from '@billing/common'
import { APP_STATUS, APP_STATUS_TEXT } from '@billing/common'
import {
  Alert,
  AlertDescription,
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertTitle,
  Badge,
  Button,
  Card,
  CardContent,
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  Input,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Textarea,
} from '@billing/ui'
import {
  Copy,
  Edit,
  Key,
  Layers,
  Plus,
  RefreshCw,
  Search,
  Trash2,
} from 'lucide-vue-next'
import { onMounted, reactive, ref, watch } from 'vue'
import { toast } from 'vue-sonner'
import { appApi } from '@/api'
import ClientLayout from '@/layouts/ClientLayout.vue'

const loading = ref(false)
const submitting = ref(false)
const searchQuery = ref('')
const statusFilter = ref('')
const sortBy = ref('created_at')

// 模态框状态
const showCreateModal = ref(false)
const showEditModal = ref(false)
const showApiKeysModal = ref(false)

// 应用数据
const applications = ref([])
const selectedApplication = ref(null)

// 对话框状态管理
const showDeleteConfirm = ref(false)
const showRegenerateConfirm = ref(false)
const showDeleteApiKeyConfirm = ref(false)
const pendingDeleteApp = ref(null)
const pendingRegenerateApiKey = ref(null)
const pendingDeleteApiKey = ref(null)

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0,
})

// 应用表单
const applicationForm = reactive({
  name: '',
  comment: '',
})

// 格式化数字
function formatNumber(num: number) {
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`
  }
  if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`
  }
  return num.toString()
}

// 格式化货币
function formatCurrency(amount: number) {
  return amount.toFixed(2)
}

// 格式化日期
function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 加载应用列表
async function loadApplications() {
  loading.value = true
  try {
    const res = await appApi.getList({
      page: pagination.page,
      page_size: pagination.pageSize,
      search: searchQuery.value,
      status: statusFilter.value,
      sort_by: sortBy.value,
    })
    applications.value = Array.isArray(res.data) ? res.data : []
    pagination.total = res.pagination.total
  }
  catch (error) {
    toast.error('加载应用列表失败')
  }
  finally {
    loading.value = false
  }
}

// 创建应用
async function submitApplication() {
  submitting.value = true
  try {
    if (showCreateModal.value) {
      await appApi.createItem(applicationForm)
      toast.success('应用创建成功')
    }
    else {
      await appApi.updateItem(selectedApplication.value.id, applicationForm)
      toast.success('应用更新成功')
    }
    closeModals()
    loadApplications()
  }
  catch (error) {
    toast.error(showCreateModal.value ? '创建失败' : '更新失败')
  }
  finally {
    submitting.value = false
  }
}

// 编辑应用
function editApplication(app) {
  selectedApplication.value = app
  applicationForm.name = app.name
  applicationForm.comment = app.comment || ''
  showEditModal.value = true
}

// 删除应用
function deleteApplication(app) {
  pendingDeleteApp.value = app
  showDeleteConfirm.value = true
}

// 确认删除应用
async function confirmDeleteApplication() {
  if (!pendingDeleteApp.value)
    return

  try {
    await appApi.deleteItem(pendingDeleteApp.value.id)
    toast.success('应用删除成功')
    loadApplications()
  }
  catch (error) {
    toast.error('删除失败')
  }
  finally {
    showDeleteConfirm.value = false
    pendingDeleteApp.value = null
  }
}

// 查看API Keys
async function viewApiKeys(app: App) {
  selectedApplication.value = app
  showApiKeysModal.value = true
}

// 复制API Key
async function copyApiKey(key: string) {
  try {
    await navigator.clipboard.writeText(key)
    toast.success('API Key 已复制到剪贴板')
  }
  catch (error) {
    toast.error('复制失败', {
      description: error?.message || '未知错误',
    })
  }
}

// 重新生成API Key
function regenerateApiKey(app: App) {
  pendingRegenerateApiKey.value = app
  showRegenerateConfirm.value = true
}

// 确认重新生成API Key
async function confirmRegenerateApiKey() {
  if (!pendingRegenerateApiKey.value)
    return

  try {
    const res = await appApi.regenerateApiKey(pendingRegenerateApiKey.value.id)
    toast.success('API Key重新生成成功')
    viewApiKeys(res)
  }
  catch (error) {
    toast.error('重新生成失败', {
      description: error?.message || '未知错误',
    })
  }
  finally {
    showRegenerateConfirm.value = false
    pendingRegenerateApiKey.value = null
  }
}

// 删除API Key
async function deleteApp(app: string) {
  pendingDeleteApp.value = app
  showDeleteApiKeyConfirm.value = true
}

async function confirmDeleteApiKey() {
  if (!pendingDeleteApiKey.value)
    return

  try {
    await appApi.deleteItem(pendingDeleteApp.value.id)
    toast.success('API Key删除成功')
    viewApiKeys(selectedApplication.value)
  }
  catch (error) {
    toast.error('删除失败')
  }
  finally {
    showDeleteApiKeyConfirm.value = false
    pendingDeleteApp.value = null
  }
}

function resetFilter() {
  searchQuery.value = ''
  statusFilter.value = ''
  sortBy.value = 'created_at'
}

// 关闭模态框
function closeModals() {
  showCreateModal.value = false
  showEditModal.value = false
  selectedApplication.value = null
  applicationForm.name = ''
  applicationForm.comment = ''
}

// 切换页面
function changePage(page: number) {
  pagination.page = page
  loadApplications()
}

// 监听搜索和筛选变化
watch([searchQuery, statusFilter, sortBy], () => {
  pagination.page = 1
  loadApplications()
})

// 初始化
onMounted(() => {
  loadApplications()
})
</script>

<template>
  <ClientLayout>
    <div class="space-y-6">
      <!-- 页面标题 -->
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">
            应用管理
          </h1>
          <p class="text-gray-600">
            管理您的所有应用
          </p>
        </div>
        <div class="flex items-center space-x-3">
          <Button
            variant="outline"
            :disabled="loading"
            @click="loadApplications"
          >
            <RefreshCw
              class="w-4 h-4"
              :class="[{ 'animate-spin': loading }]"
            />
            刷新
          </Button>
          <Button @click="showCreateModal = true">
            <Plus class="w-4 h-4" />
            创建应用
          </Button>
        </div>
      </div>

      <!-- 搜索和筛选 -->
      <Card>
        <CardContent class="flex items-center justify-between">
          <div class="flex gap-6">
            <div>
              <Label class="text-sm font-medium mb-2">
                搜索应用
              </Label>
              <div class="relative">
                <Input
                  v-model="searchQuery"
                  type="text"
                  placeholder="输入应用名称或描述"
                  class="pl-10"
                />
                <Search class="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
              </div>
            </div>
            <div>
              <Label class="text-sm font-medium mb-2">
                状态筛选
              </Label>
              <Select v-model="statusFilter">
                <SelectTrigger>
                  <SelectValue placeholder="全部状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem :value="APP_STATUS.OK">
                    活跃
                  </SelectItem>
                  <SelectItem :value="APP_STATUS.BLOCKED">
                    停用
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label class="text-sm font-medium mb-2">
                排序方式
              </Label>
              <Select v-model="sortBy">
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="created_at">
                    创建时间
                  </SelectItem>
                  <SelectItem value="name">
                    应用名称
                  </SelectItem>
                  <SelectItem value="usage">
                    使用量
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <Button
            variant="outline"
            @click="resetFilter"
          >
            重置
          </Button>
        </CardContent>
      </Card>

      <!-- 应用列表 -->
      <Card class="p-0 overflow-hidden">
        <CardContent class="p-0">
          <div
            v-if="applications.length === 0 && !loading"
            class="text-center py-12"
          >
            <Layers class="mx-auto h-12 w-12 text-gray-400" />
            <h3 class="mt-2 text-sm font-medium text-gray-900">
              暂无应用
            </h3>
            <p class="mt-1 text-sm text-gray-500">
              创建您的第一个应用开始使用AI服务
            </p>
            <div class="mt-6">
              <Button @click="showCreateModal = true">
                <Plus class="w-4 h-4" />
                创建应用
              </Button>
            </div>
          </div>

          <div
            v-else
            class="divide-y divide-gray-200"
          >
            <div
              v-for="app in applications"
              :key="app.id"
              class="p-6 hover:bg-gray-50"
            >
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                  <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Layers class="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 class="text-lg font-medium text-gray-900">
                      {{ app.name }}
                    </h3>
                    <p class="text-sm text-gray-500">
                      {{ app.comment }}
                    </p>
                    <div class="flex items-center space-x-4 mt-2">
                      <Badge
                        :class="{
                          'bg-success text-success-foreground': app.status === APP_STATUS.OK,
                          'bg-destructive text-destructive-foreground': app.status === APP_STATUS.BLOCKED,
                        }"
                      >
                        {{ APP_STATUS_TEXT[app.status] }}
                      </Badge>
                      <span class="text-sm text-gray-500">
                        创建于 {{ formatDate(app.created_at) }}
                      </span>
                    </div>
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  <Button
                    size="sm"
                    variant="outline"
                    @click="regenerateApiKey(app)"
                  >
                    <Key class="w-4 h-4" />
                    重新生成 API Key
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    @click="viewApiKeys(app)"
                  >
                    <Key class="w-4 h-4" />
                    API Key
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    @click="editApplication(app)"
                  >
                    <Edit class="w-4 h-4" />
                    编辑
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    class="border-red-600 text-red-600 hover:bg-red-600 hover:text-white"
                    @click="deleteApplication(app)"
                  >
                    <Trash2 class="w-4 h-4" />
                    删除
                  </Button>
                </div>
              </div>

              <!-- 使用统计 -->
              <div class="mt-4 grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card>
                  <CardContent class="px-6">
                    <div class="text-sm font-medium text-gray-500">
                      今日调用
                    </div>
                    <div class="text-xl font-semibold text-gray-900">
                      {{ formatNumber(app.stats?.today_calls || 0) }}
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent class="px-6">
                    <div class="text-sm font-medium text-gray-500">
                      本月调用
                    </div>
                    <div class="text-xl font-semibold text-gray-900">
                      {{ formatNumber(app.stats?.month_calls || 0) }}
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent class="px-6">
                    <div class="text-sm font-medium text-gray-500">
                      今日费用
                    </div>
                    <div class="text-xl font-semibold text-gray-900">
                      ¥{{ formatCurrency(app.stats?.today_cost || 0) }}
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent class="px-6">
                    <div class="text-sm font-medium text-gray-500">
                      本月费用
                    </div>
                    <div class="text-xl font-semibold text-gray-900">
                      ¥{{ formatCurrency(app.stats?.month_cost || 0) }}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- 分页 -->
      <div
        v-if="pagination.total > pagination.pageSize"
        class="flex items-center justify-between"
      >
        <div class="text-sm text-gray-700">
          显示第 {{ (pagination.page - 1) * pagination.pageSize + 1 }} 到
          {{ Math.min(pagination.page * pagination.pageSize, pagination.total) }} 条，
          共 {{ pagination.total }} 条记录
        </div>
        <div class="flex items-center space-x-2">
          <Button
            variant="outline"
            :disabled="pagination.page <= 1"
            @click="changePage(pagination.page - 1)"
          >
            上一页
          </Button>
          <Button
            variant="outline"
            :disabled="pagination.page >= Math.ceil(pagination.total / pagination.pageSize)"
            @click="changePage(pagination.page + 1)"
          >
            下一页
          </Button>
        </div>
      </div>
    </div>

    <!-- 创建/编辑应用模态框 -->
    <Dialog
      :open="showCreateModal || showEditModal"
      @update:open="closeModals"
    >
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            {{ showCreateModal ? '创建应用' : '编辑应用' }}
          </DialogTitle>
        </DialogHeader>
        <form
          class="space-y-4"
          @submit.prevent="submitApplication"
        >
          <div>
            <Label class="text-sm font-medium mb-2">
              应用名称
            </Label>
            <Input
              v-model="applicationForm.name"
              type="text"
              required
              placeholder="输入应用名称"
            />
          </div>
          <div>
            <Label class="text-sm font-medium mb-2">
              应用备注
            </Label>
            <Textarea
              v-model="applicationForm.comment"
              rows="3"
              placeholder="输入应用备注（可选）"
            />
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              @click="closeModals"
            >
              取消
            </Button>
            <Button
              type="submit"
              :disabled="submitting"
            >
              {{ submitting ? '保存中...' : '保存' }}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>

    <!-- API Keys模态框 -->
    <Dialog
      :open="showApiKeysModal"
      @update:open="(open) => showApiKeysModal = open"
    >
      <DialogContent class="max-w-4xl">
        <DialogHeader>
          <DialogTitle>
            {{ selectedApplication?.name }} - API Key
          </DialogTitle>
        </DialogHeader>
        <div class="flex items-center justify-between bg-gray-100 p-4 rounded-lg">
          {{ selectedApplication.api_key }}
          <Copy
            class="w-4 h-4 cursor-pointer"
            @click="copyApiKey(selectedApplication.api_key)"
          />
        </div>
        <Alert variant="destructive">
          <AlertTitle>
            重要提示
          </AlertTitle>
          <AlertDescription>
            请妥善保管您的 API Key，不要泄露给他人。
          </AlertDescription>
        </Alert>
      </DialogContent>
    </Dialog>

    <!-- 重新生成 API Key 确认对话框 -->
    <AlertDialog
      :open="showRegenerateConfirm"
      @update:open="(open) => showRegenerateConfirm = open"
    >
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>确认重新生成 API Key</AlertDialogTitle>
          <AlertDialogDescription>
            确定要重新生成应用【{{ pendingRegenerateApiKey?.name }}】的 API Key 吗？原 Key 将失效。
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel @click="showRegenerateConfirm = false">
            取消
          </AlertDialogCancel>
          <AlertDialogAction @click="confirmRegenerateApiKey">
            确认重新生成
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>

    <!-- 删除 API Key 确认对话框 -->
    <AlertDialog v-model:open="showDeleteApiKeyConfirm">
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>确认删除 API Key</AlertDialogTitle>
          <AlertDialogDescription>
            确定要删除应用【{{ pendingDeleteApiKey?.name }}】吗？此操作不可撤销。
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>
            取消
          </AlertDialogCancel>
          <AlertDialogAction
            class="bg-red-600 hover:bg-red-700"
            @click="confirmDeleteApiKey"
          >
            确认删除
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>

    <!-- 删除应用确认对话框 -->
    <AlertDialog
      :open="showDeleteConfirm"
      @update:open="(open) => showDeleteConfirm = open"
    >
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>确认删除应用</AlertDialogTitle>
          <AlertDialogDescription>
            确定要删除应用【{{ pendingDeleteApp?.name }}】吗？此操作不可撤销，将导致应用 Api Key 失效。
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel @click="showDeleteConfirm = false">
            取消
          </AlertDialogCancel>
          <AlertDialogAction
            class="bg-red-600 hover:bg-red-700"
            @click="confirmDeleteApplication"
          >
            确认删除
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  </ClientLayout>
</template>
