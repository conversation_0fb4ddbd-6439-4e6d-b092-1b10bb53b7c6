<script setup lang="ts">
import {
  <PERSON>ton,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Checkbox,
  Input,
  Label,
  Separator,
} from '@billing/ui'
import {
  Eye,
  EyeOff,
  Github,
  Loader2,
  Mail,
  Zap,
} from 'lucide-vue-next'
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { toast } from 'vue-sonner'
import { authApi } from '@/api/auth'
import { useUserStore } from '@/store'

const router = useRouter()
const userStore = useUserStore()

const loading = ref(false)
const showPassword = ref(false)

const form = reactive({
  email: '',
  password: '',
  rememberMe: false,
})

const errors = reactive({
  email: '',
  password: '',
})

function validateForm() {
  errors.email = ''
  errors.password = ''

  if (!form.email) {
    errors.email = '请输入邮箱地址'
    return false
  }

  if (!/^[^\s@]+@[^\s@][^\s.@]*\.[^\s@]+$/.test(form.email)) {
    errors.email = '请输入有效的邮箱地址'
    return false
  }

  if (!form.password) {
    errors.password = '请输入密码'
    return false
  }

  if (form.password.length < 4) {
    errors.password = '密码长度至少4位'
    return false
  }

  return true
}

async function handleLogin() {
  if (!validateForm()) {
    return
  }

  loading.value = true

  try {
    const res = await authApi.login({
      email: form.email,
      password: form.password,
    })
    userStore.setUser(res.user)
    userStore.setToken(res.token)
    toast.success('登录成功！')
    router.push('/dashboard')
  }
  catch (error: any) {
    toast.error(error.message || '登录失败，请重试')
  }
  finally {
    loading.value = false
  }
}
</script>

<template>
  <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <div class="flex justify-center">
        <RouterLink
          to="/"
          class="flex items-center space-x-2"
        >
          <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
            <Zap class="w-6 h-6 text-white" />
          </div>
          <span class="text-xl font-bold text-gray-900">柚子 AI</span>
        </RouterLink>
      </div>
      <p class="mt-6 text-center text-sm text-muted-foreground">
        还没有账户？
        <RouterLink
          to="/auth/register"
          class="font-medium text-primary hover:text-primary/80 transition-colors underline-offset-4 hover:underline"
        >
          立即注册
        </RouterLink>
      </p>
    </div>

    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
      <Card class="shadow-lg">
        <CardHeader class="space-y-1">
          <CardTitle class="text-2xl text-center">
            登录您的账户
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form
            class="space-y-6"
            @submit.prevent="handleLogin"
          >
            <div class="space-y-2">
              <Label for="email">邮箱地址</Label>
              <div class="relative">
                <Input
                  id="email"
                  v-model="form.email"
                  type="email"
                  placeholder="请输入邮箱地址"
                  :class="{
                    'border-destructive focus-visible:ring-destructive': errors.email,
                  }"
                  class="pr-10"
                />
                <Mail class="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              </div>
              <p
                v-if="errors.email"
                class="text-sm text-destructive"
              >
                {{ errors.email }}
              </p>
            </div>

            <div class="space-y-2">
              <Label for="password">密码</Label>
              <div class="relative">
                <Input
                  id="password"
                  v-model="form.password"
                  :type="showPassword ? 'text' : 'password'"
                  placeholder="请输入密码"
                  :class="{
                    'border-destructive focus-visible:ring-destructive': errors.password,
                  }"
                  class="pr-10"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  class="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0 text-muted-foreground hover:text-foreground"
                  @click="showPassword = !showPassword"
                >
                  <Eye
                    v-if="!showPassword"
                    class="h-4 w-4"
                  />
                  <EyeOff
                    v-else
                    class="h-4 w-4"
                  />
                </Button>
              </div>
              <p
                v-if="errors.password"
                class="text-sm text-destructive"
              >
                {{ errors.password }}
              </p>
            </div>

            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-2">
                <Checkbox
                  id="remember-me"
                  v-model:checked="form.rememberMe"
                />
                <Label
                  for="remember-me"
                  class="text-sm font-normal cursor-pointer"
                >
                  记住我
                </Label>
              </div>

              <div class="text-sm">
                <RouterLink
                  to="/auth/forgot-password"
                  class="font-medium text-primary hover:text-primary/80 transition-colors"
                >
                  忘记密码？
                </RouterLink>
              </div>
            </div>

            <div>
              <Button
                type="submit"
                :disabled="loading"
                class="w-full"
              >
                <span
                  v-if="loading"
                  class="absolute left-0 inset-y-0 flex items-center pl-3"
                >
                  <Loader2 class="h-4 w-4 animate-spin" />
                </span>
                {{ loading ? '登录中...' : '登录' }}
              </Button>
            </div>
          </form>

          <div class="mt-6">
            <div class="relative">
              <div class="absolute inset-0 flex items-center">
                <Separator class="w-full" />
              </div>
              <div class="relative flex justify-center text-xs uppercase">
                <span class="bg-background px-2 text-muted-foreground">或者</span>
              </div>
            </div>

            <div class="mt-6 grid grid-cols-2 gap-3">
              <Button
                type="button"
                variant="outline"
                class="w-full"
              >
                <Github class="h-4 w-4 mr-2" />
                GitHub
              </Button>

              <Button
                type="button"
                variant="outline"
                class="w-full"
              >
                <svg
                  class="h-4 w-4 mr-2"
                  viewBox="0 0 24 24"
                >
                  <path
                    fill="currentColor"
                    d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                  />
                  <path
                    fill="currentColor"
                    d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                  />
                  <path
                    fill="currentColor"
                    d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                  />
                  <path
                    fill="currentColor"
                    d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                  />
                </svg>
                Google
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>
