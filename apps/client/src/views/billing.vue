<script setup lang="ts">
import type { <PERSON>ing<PERSON>eriod, <PERSON>ing<PERSON>uery, BillingStats } from '@/types/billing'
import {
  Badge,
  Button,
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Input,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@billing/ui'
import {
  ArcElement,
  CategoryScale,
  Chart as ChartJS,
  Legend,
  LinearScale,
  LineElement,
  PointElement,
  Title,
  Tooltip,
} from 'chart.js'
import {
  Activity,
  DollarSign,
  Download,
  RefreshCw,
  TrendingUp,
  Zap,
} from 'lucide-vue-next'
import { computed, onMounted, reactive, ref, watch } from 'vue'
import {
  Doughnut,
  Line,
} from 'vue-chartjs'
import { toast } from 'vue-sonner'
import { appApi, billingApi } from '@/api'
import ClientLayout from '@/layouts/ClientLayout.vue'

// 注册Chart.js组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
)

const formatter = new Intl.DateTimeFormat('zh-CN', {
  year: 'numeric',
  month: '2-digit',
  day: '2-digit',
  hour: '2-digit',
  hour12: false, // 使用24小时制
  timeZone: 'Asia/Shanghai',
})

const loading = ref(false)
const timeRange = ref('30d')
const customStartDate = ref('')
const customEndDate = ref('')
const selectedApp = ref('')
const trendMetric = ref('calls')

// 应用列表
const applications = ref([])

// 统计数据
const billingStats = reactive<Partial<BillingStats>>({
  total_calls: 0,
  total_usage: {
    llm: 0,
    tts: 0,
    asr: 0,
  },
  total_cost: 0,
  avg_cost_per_call: 0,
  daily_average_cost: 0,
})

// 按模块统计数据
const moduleStats = reactive({
  llm: {
    calls: 0,
    usage: 0,
    cost: 0,
    avg_cost_per_call: 0,
  },
  tts: {
    calls: 0,
    usage: 0,
    cost: 0,
    avg_cost_per_call: 0,
  },
  asr: {
    calls: 0,
    usage: 0,
    cost: 0,
    avg_cost_per_call: 0,
  },
})

// 趋势数据
const trendData = reactive({
  labels: [],
  datasets: [{ data: [] }],
})

// 按模块的趋势数据
const moduleTrendData = reactive({
  labels: [],
  datasets: [],
})

// 服务分布数据
const serviceData = reactive({
  labels: [],
  datasets: [{ data: [], background_color: [], border_width: 0 }],
})

// 详细记录
const billingRecords = ref<BillingPeriod[]>([])
const recordPagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0,
})

// 图表配置
const trendChartData = computed(() => ({
  labels: trendData.labels,
  datasets: [
    {
      label: getTrendLabel(),
      data: trendData.datasets[0]?.data || [],
      borderColor: 'rgb(59, 130, 246)',
      backgroundColor: 'rgba(59, 130, 246, 0.1)',
      tension: 0.4,
    },
  ],
}))

const trendChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false,
    },
  },
  scales: {
    y: {
      beginAtZero: true,
    },
  },
}

// 按模块趋势图表配置
const moduleTrendChartData = computed(() => ({
  labels: moduleTrendData.labels,
  datasets: [
    {
      label: 'LLM',
      data: moduleTrendData.datasets.find(d => d.label === 'LLM')?.data || [],
      borderColor: 'rgb(59, 130, 246)',
      backgroundColor: 'rgba(59, 130, 246, 0.1)',
      tension: 0.4,
    },
    {
      label: 'TTS',
      data: moduleTrendData.datasets.find(d => d.label === 'TTS')?.data || [],
      borderColor: 'rgb(16, 185, 129)',
      backgroundColor: 'rgba(16, 185, 129, 0.1)',
      tension: 0.4,
    },
    {
      label: 'ASR',
      data: moduleTrendData.datasets.find(d => d.label === 'ASR')?.data || [],
      borderColor: 'rgb(139, 92, 246)',
      backgroundColor: 'rgba(139, 92, 246, 0.1)',
      tension: 0.4,
    },
  ],
}))

const moduleTrendChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: true,
      position: 'top' as const,
    },
  },
  scales: {
    y: {
      beginAtZero: true,
    },
  },
}

const serviceChartData = computed(() => ({
  labels: serviceData.labels,
  datasets: [
    {
      data: serviceData.datasets[0]?.data || [],
      backgroundColor: serviceData.datasets[0]?.background_color || [],
      borderWidth: 0,
    },
  ],
}))

const serviceChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom' as const,
    },
  },
} as any

// 获取趋势图标签
function getTrendLabel() {
  switch (trendMetric.value) {
    case 'calls':
      return '调用次数'
    case 'tokens':
      return 'Token使用量'
    case 'cost':
      return '费用 (¥)'
    default:
      return ''
  }
}

// 格式化数字
function formatNumber(num: number) {
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`
  }
  if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`
  }
  return num.toString()
}

// 格式化货币
function formatCurrency(amount: number) {
  return amount.toFixed(2)
}

// 格式化日期时间
function formatDateTime(dateString: string | number) {
  return formatter.format(new Date(dateString))
}

// 获取服务类型名称
function getServiceTypeName(type: string) {
  const names = {
    llm: 'LLM',
    tts: 'TTS',
    asr: 'ASR',
  }
  return names[type] || type
}

// 获取服务类型颜色
function getServiceTypeColor(type: string) {
  const colors = {
    llm: 'bg-blue-100 text-blue-800',
    tts: 'bg-green-100 text-green-800',
    asr: 'bg-purple-100 text-purple-800',
  }
  return colors[type] || 'bg-gray-100 text-gray-800'
}

function getUsageUnit(module: string) {
  if (module === 'llm') {
    return 'Token'
  }
  if (module === 'tts') {
    return '字符'
  }
  if (module === 'asr') {
    return '秒'
  }

  return ''
}

// 时间范围变化
function onTimeRangeChange() {
  if (timeRange.value !== 'custom') {
    loadBillingData()
  }
}

// 加载应用列表
async function loadApplications() {
  try {
    const res = await appApi.getList({
      page: 1,
      page_size: 10,
    })
    applications.value = Array.isArray(res.data) ? res.data : []
  }
  catch (error) {
    console.error('Failed to load applications:', error)
  }
}

// 加载统计数据
async function loadBillingStats() {
  try {
    const data = await billingApi.getBillingStats(getQueryParams())
    Object.assign(billingStats, data || {})
  }
  catch (error) {
    console.error('Failed to load billing stats:', error)
  }
}

// 加载按模块统计数据
async function loadModuleStats() {
  try {
    const params = getQueryParams()
    const data = await billingApi.getModuleStats(params)
    Object.assign(moduleStats, data || {})
  }
  catch (error) {
    console.error('Failed to load module stats:', error)
  }
}

// 加载趋势数据
async function loadTrendData() {
  try {
    const params = {
      ...getQueryParams(),
      metric: trendMetric.value,
    }
    const data = await billingApi.getUsageTrends(params)
    Object.assign(trendData, data)
  }
  catch (error) {
    console.error('Failed to load trend data:', error)
  }
}

// 加载按模块趋势数据
async function loadModuleTrendData() {
  try {
    const params = {
      ...getQueryParams(),
      metric: trendMetric.value,
      groupBy: 'module',
    }
    const data = await billingApi.getModuleTrends(params)
    Object.assign(moduleTrendData, data)
  }
  catch (error) {
    console.error('Failed to load module trend data:', error)
  }
}

// 加载服务分布数据
async function loadServiceData() {
  try {
    const params = getQueryParams()
    const data = await billingApi.getServiceUsage(params)
    Object.assign(serviceData, data)
  }
  catch (error) {
    console.error('Failed to load service data:', error)
  }
}

// 加载详细记录
async function loadBillingRecords() {
  try {
    const params = {
      ...getQueryParams(),
      page: recordPagination.page,
      pageSize: recordPagination.pageSize,
    }
    const res = await billingApi.getBillingPeriods(params)
    billingRecords.value = res.data || []
    recordPagination.total = res.pagination.total
    recordPagination.pageSize = res.pagination.per_page
    recordPagination.page = res.pagination.current_page
  }
  catch (error) {
    console.error('Failed to load billing records:', error)
  }
}

// 获取查询参数
function getQueryParams() {
  const params = {} as BillingQuery

  if (timeRange.value === 'custom') {
    if (customStartDate.value)
      params.start_date = customStartDate.value
    if (customEndDate.value)
      params.end_date = customEndDate.value
  }
  else {
    params.period = timeRange.value as 'today' | '7d' | '30d' | '90d' | 'custom'
  }

  if (selectedApp.value) {
    params.app_id = selectedApp.value
  }

  return params
}

function resetFilter() {
  timeRange.value = '30d'
  customStartDate.value = ''
  customEndDate.value = ''
  selectedApp.value = ''
  loadBillingData()
}

// 加载所有计费数据
async function loadBillingData() {
  loading.value = true
  try {
    await Promise.all([
      loadBillingStats(),
      loadModuleStats(),
      loadTrendData(),
      loadModuleTrendData(),
      loadServiceData(),
      loadBillingRecords(),
    ])
  }
  catch (error) {
    toast.error('加载数据失败')
  }
  finally {
    loading.value = false
  }
}

// 刷新数据
function refreshData() {
  loadBillingData()
}

// 导出报告
async function exportReport() {
  try {
    const params = getQueryParams()
    const response = await billingApi.exportBillingReport(params)

    // 创建 Blob 对象
    const blob = new Blob([response], { type: 'text/csv' })

    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')

    // 设置文件名
    const filename = 'billing-report.csv'
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()

    // 清理资源
    link.remove()
    window.URL.revokeObjectURL(url)

    toast.success('报告导出成功')
  }
  catch (error) {
    toast.error('导出失败')
  }
}

// 切换记录页面
function changeRecordPage(page: number) {
  recordPagination.page = page
  loadBillingRecords()
}

// 监听趋势指标变化
watch(trendMetric, () => {
  loadTrendData()
  loadModuleTrendData()
})

// 监听自定义日期变化
watch([customStartDate, customEndDate], () => {
  if (timeRange.value === 'custom' && customStartDate.value && customEndDate.value) {
    loadBillingData()
  }
})

// 初始化
onMounted(() => {
  loadApplications()
  loadBillingData()
})
</script>

<template>
  <ClientLayout>
    <div class="space-y-6">
      <!-- 页面标题 -->
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">
            计费统计
          </h1>
          <p class="text-gray-600">
            查看详细的使用统计和费用分析
          </p>
        </div>
        <div class="flex items-center space-x-3">
          <Button
            variant="outline"
            @click="exportReport"
          >
            <Download class="w-4 h-4 mr-2" />
            导出报告
          </Button>
          <Button
            variant="outline"
            :disabled="loading"
            @click="refreshData"
          >
            <RefreshCw
              class="w-4 h-4 mr-2"
              :class="[{ 'animate-spin': loading }]"
            />
            刷新
          </Button>
        </div>
      </div>

      <!-- 时间范围选择 -->
      <Card>
        <CardContent class="flex items-center justify-between">
          <div class="flex gap-6">
            <div>
              <Label class="block text-sm font-medium mb-2">
                时间范围
              </Label>
              <Select
                v-model="timeRange"
                @update:model-value="onTimeRangeChange"
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="today">
                    今天
                  </SelectItem>
                  <SelectItem value="7d">
                    最近7天
                  </SelectItem>
                  <SelectItem value="30d">
                    最近30天
                  </SelectItem>
                  <SelectItem value="90d">
                    最近90天
                  </SelectItem>
                  <SelectItem value="custom">
                    自定义
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div v-if="timeRange === 'custom'">
              <Label class="block text-sm font-medium mb-2">
                开始日期
              </Label>
              <Input
                v-model="customStartDate"
                type="date"
              />
            </div>
            <div v-if="timeRange === 'custom'">
              <Label class="block text-sm font-medium mb-2">
                结束日期
              </Label>
              <Input
                v-model="customEndDate"
                type="date"
              />
            </div>
            <div>
              <Label class="block text-sm font-medium mb-2">
                应用筛选
              </Label>
              <Select
                v-model="selectedApp"
                @update:model-value="loadBillingData"
              >
                <SelectTrigger>
                  <SelectValue placeholder="全部应用" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem
                    v-for="app in applications"
                    :key="app.id"
                    :value="app.id"
                  >
                    {{ app.name }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <Button
            variant="outline"
            @click="resetFilter"
          >
            重置
          </Button>
        </CardContent>
      </Card>

      <!-- 统计概览 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent>
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                  <Activity class="w-5 h-5 text-white" />
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">
                    总调用次数
                  </dt>
                  <dd class="text-xl font-medium text-gray-900">
                    {{ formatNumber(billingStats.total_calls) }}
                  </dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent>
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                  <Zap class="w-5 h-5 text-white" />
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">
                    每日平均费用
                  </dt>
                  <dd class="text-lg font-medium flex items-center gap-2">
                    ¥{{ formatCurrency(billingStats.daily_average_cost) }}
                  </dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent>
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                  <DollarSign class="w-5 h-5 text-white" />
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">
                    总费用
                  </dt>
                  <dd class="text-xl font-medium text-gray-900">
                    ¥{{ formatCurrency(billingStats.total_cost) }}
                  </dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent>
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                  <TrendingUp class="w-5 h-5 text-white" />
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">
                    平均单价
                  </dt>
                  <dd class="text-xl font-medium text-gray-900">
                    ¥{{ formatCurrency(billingStats.avg_cost_per_call) }}
                  </dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- 按模块统计 -->
      <Card>
        <CardHeader>
          <CardTitle>模块统计</CardTitle>
          <CardDescription>各服务模块的详细使用情况</CardDescription>
        </CardHeader>
        <CardContent>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- LLM 模块 -->
            <div class="bg-blue-50 rounded-lg p-4 border border-blue-200">
              <div class="flex items-center justify-between mb-3">
                <h3 class="text-lg font-semibold text-blue-800">
                  LLM 大语言模型
                </h3>
                <Badge class="bg-blue-100 text-blue-800">
                  LLM
                </Badge>
              </div>
              <div class="space-y-2">
                <div class="flex justify-between">
                  <span class="text-sm text-gray-600">调用次数</span>
                  <span class="font-medium">{{ formatNumber(moduleStats.llm.calls) }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm text-gray-600">Token使用</span>
                  <span class="font-medium">{{ formatNumber(moduleStats.llm.usage) }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm text-gray-600">总费用</span>
                  <span class="font-medium text-blue-600">¥{{ formatCurrency(moduleStats.llm.cost) }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm text-gray-600">平均单价</span>
                  <span class="font-medium">¥{{ formatCurrency(moduleStats.llm.avg_cost_per_call) }}</span>
                </div>
              </div>
            </div>

            <!-- TTS 模块 -->
            <div class="bg-green-50 rounded-lg p-4 border border-green-200">
              <div class="flex items-center justify-between mb-3">
                <h3 class="text-lg font-semibold text-green-800">
                  TTS 语音合成
                </h3>
                <Badge class="bg-green-100 text-green-800">
                  TTS
                </Badge>
              </div>
              <div class="space-y-2">
                <div class="flex justify-between">
                  <span class="text-sm text-gray-600">调用次数</span>
                  <span class="font-medium">{{ formatNumber(moduleStats.tts.calls) }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm text-gray-600">字符处理</span>
                  <span class="font-medium">{{ formatNumber(moduleStats.tts.usage) }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm text-gray-600">总费用</span>
                  <span class="font-medium text-green-600">¥{{ formatCurrency(moduleStats.tts.cost) }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm text-gray-600">平均单价</span>
                  <span class="font-medium">¥{{ formatCurrency(moduleStats.tts.avg_cost_per_call) }}</span>
                </div>
              </div>
            </div>

            <!-- ASR 模块 -->
            <div class="bg-purple-50 rounded-lg p-4 border border-purple-200">
              <div class="flex items-center justify-between mb-3">
                <h3 class="text-lg font-semibold text-purple-800">
                  ASR 语音识别
                </h3>
                <Badge class="bg-purple-100 text-purple-800">
                  ASR
                </Badge>
              </div>
              <div class="space-y-2">
                <div class="flex justify-between">
                  <span class="text-sm text-gray-600">调用次数</span>
                  <span class="font-medium">{{ formatNumber(moduleStats.asr.calls) }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm text-gray-600">音频时长</span>
                  <span class="font-medium">{{ formatNumber(moduleStats.asr.usage) }}秒</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm text-gray-600">总费用</span>
                  <span class="font-medium text-purple-600">¥{{ formatCurrency(moduleStats.asr.cost) }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm text-gray-600">平均单价</span>
                  <span class="font-medium">¥{{ formatCurrency(moduleStats.asr.avg_cost_per_call) }}</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- 图表区域 -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>使用趋势</CardTitle>
            <CardDescription>过去一段时间的使用变化</CardDescription>
          </CardHeader>
          <CardContent>
            <div class="flex items-center space-x-2 mb-4">
              <Button
                :variant="trendMetric === 'calls' ? 'default' : 'ghost'"
                size="sm"
                @click="trendMetric = 'calls'"
              >
                调用次数
              </Button>
              <Button
                :variant="trendMetric === 'cost' ? 'default' : 'ghost'"
                size="sm"
                @click="trendMetric = 'cost'"
              >
                费用
              </Button>
            </div>
            <div class="h-64">
              <Line
                v-if="trendData.labels.length > 0"
                :data="trendChartData"
                :options="trendChartOptions"
              />
              <div
                v-else
                class="flex items-center justify-center h-full text-gray-500"
              >
                暂无数据
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>模块趋势对比</CardTitle>
            <CardDescription>各模块使用趋势对比</CardDescription>
          </CardHeader>
          <CardContent>
            <div class="h-64">
              <Line
                v-if="moduleTrendData.labels.length > 0"
                :data="moduleTrendChartData"
                :options="moduleTrendChartOptions"
              />
              <div
                v-else
                class="flex items-center justify-center h-full text-gray-500"
              >
                暂无数据
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>服务费用分布</CardTitle>
            <CardDescription>各服务类型的费用占比</CardDescription>
          </CardHeader>
          <CardContent>
            <div class="h-64">
              <Doughnut
                v-if="serviceData.labels.length > 0"
                :data="serviceChartData"
                :options="serviceChartOptions"
              />
              <div
                v-else
                class="flex items-center justify-center h-full text-gray-500"
              >
                暂无数据
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- 账单表格 -->
      <Card>
        <CardHeader>
          <CardTitle>账单记录</CardTitle>
          <CardDescription>查看所有的账单记录</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>时间</TableHead>
                <TableHead>服务类型</TableHead>
                <TableHead>调用次数</TableHead>
                <TableHead>使用量</TableHead>
                <TableHead>费用</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow
                v-for="record in billingRecords"
                :key="record.id"
              >
                <TableCell>{{ formatDateTime(record.created_at) }} - {{ formatDateTime(record.created_at + 3600 * 1000) }}</TableCell>
                <TableCell>
                  <Badge
                    :class="[
                      getServiceTypeColor(record.module),
                    ]"
                  >
                    {{ getServiceTypeName(record.module) }}
                  </Badge>
                </TableCell>
                <TableCell>{{ formatNumber(record.calls) }}</TableCell>
                <TableCell>{{ formatNumber(record.usage) }}{{ getUsageUnit(record.module) }}</TableCell>
                <TableCell>¥{{ formatCurrency(record.cost) }}</TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <!-- 分页 -->
      <div
        v-if="recordPagination.total > recordPagination.pageSize"
        class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6"
      >
        <div class="flex-1 flex justify-between sm:hidden">
          <Button
            variant="outline"
            :disabled="recordPagination.page <= 1"
            @click="changeRecordPage(recordPagination.page - 1)"
          >
            上一页
          </Button>
          <Button
            variant="outline"
            :disabled="recordPagination.page >= Math.ceil(recordPagination.total / recordPagination.pageSize)"
            @click="changeRecordPage(recordPagination.page + 1)"
          >
            下一页
          </Button>
        </div>
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p class="text-sm text-gray-700">
              显示第
              <span class="font-medium">{{ (recordPagination.page - 1) * recordPagination.pageSize + 1 }}</span>
              到
              <span class="font-medium">{{ Math.min(recordPagination.page * recordPagination.pageSize, recordPagination.total) }}</span>
              条，共
              <span class="font-medium">{{ recordPagination.total }}</span>
              条记录
            </p>
          </div>
          <div>
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
              <Button
                variant="outline"
                :disabled="recordPagination.page <= 1"
                @click="changeRecordPage(recordPagination.page - 1)"
              >
                上一页
              </Button>
              <Button
                variant="outline"
                :disabled="recordPagination.page >= Math.ceil(recordPagination.total / recordPagination.pageSize)"
                @click="changeRecordPage(recordPagination.page + 1)"
              >
                下一页
              </Button>
            </nav>
          </div>
        </div>
      </div>
    </div>
  </ClientLayout>
</template>
