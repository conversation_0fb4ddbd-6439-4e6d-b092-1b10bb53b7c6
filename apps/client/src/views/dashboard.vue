<script setup lang="ts">
import type { BillingQuery, BillingStats } from '@/types/billing'
import {
  <PERSON>ge,
  <PERSON>ton,
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  Input,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@billing/ui'
import {
  ArcElement,
  CategoryScale,
  Chart as ChartJS,
  Legend,
  LinearScale,
  LineElement,
  PointElement,
  Title,
  Tooltip,
} from 'chart.js'
import {
  BarChart3,
  DollarSign,
  FileText,
  HelpCircle,
  Layers,
  Plus,
  RefreshCw,
  Zap,
} from 'lucide-vue-next'
import { computed, onMounted, reactive, ref } from 'vue'
import {
  Doughnut,
  Line,
} from 'vue-chartjs'
import { toast } from 'vue-sonner'
import { appApi, billingApi } from '@/api'
import ClientLayout from '@/layouts/ClientLayout.vue'
import { useUserStore } from '@/store'

// 注册Chart.js组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
)

const userStore = useUserStore()
const loading = ref(false)
const trendPeriod = ref('7d')

// 对话框状态管理
const showQuickActionDialog = ref(false)
const showCustomPeriodDialog = ref(false)
const customPeriodForm = reactive({
  start_date: '',
  end_date: '',
})

// 统计数据
const stats = reactive<Partial<BillingStats>>({
  monthly_cost_growth: 0,
  monthly_cost: 0,
  app_count: 0,
  today_cost: 0,
  today_cost_growth: 0,
  daily_average_cost: 0,
  monthly_calls: 0,
  avg_cost_per_call: 0,
})

// 趋势数据
const trendData = reactive({
  labels: [],
  datasets: [],
})

// 服务分布数据
const serviceData = reactive({
  labels: [],
  datasets: [],
})

// 最近应用
const recentApplications = ref([])

// 图表配置
const trendChartData = computed(() => ({
  labels: trendData.labels,
  datasets: [
    {
      label: trendData.datasets[0].label,
      data: trendData.datasets[0]?.data || [],
      borderColor: 'rgb(59, 130, 246)',
      backgroundColor: 'rgba(59, 130, 246, 0.1)',
      tension: 0.4,
    },
  ],
}))

const trendChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false,
    },
  },
  scales: {
    y: {
      beginAtZero: true,
    },
  },
} as any

const serviceChartData = computed(() => ({
  labels: serviceData.labels,
  datasets: [
    {
      data: serviceData.datasets[0]?.data || [],
      backgroundColor: [
        'rgba(59, 130, 246, 0.8)',
        'rgba(16, 185, 129, 0.8)',
        'rgba(139, 92, 246, 0.8)',
        'rgba(245, 158, 11, 0.8)',
      ],
      borderWidth: 0,
    },
  ],
}))

const serviceChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom' as const,
    },
  },
} as any

// 格式化数字
function formatNumber(num: number) {
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`
  }
  if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`
  }
  return num.toString()
}

// 格式化货币
function formatCurrency(amount: number) {
  return amount.toFixed(2)
}

// 加载统计数据
async function loadStats() {
  try {
    const data = await billingApi.getBillingStats({ period: 'monthly' })
    Object.assign(stats, data)
  }
  catch (error) {
    console.error('Failed to load stats:', error)
  }
}

// 加载趋势数据
async function loadTrendData() {
  try {
    const data = await billingApi.getUsageTrends({ period: trendPeriod.value as BillingQuery['period'], start_date: customPeriodForm.start_date, end_date: customPeriodForm.end_date })
    Object.assign(trendData, data)
  }
  catch (error) {
    console.error('Failed to load trend data:', error)
  }
}

// 加载服务分布数据
async function loadServiceData() {
  try {
    const data = await billingApi.getServiceUsage({ period: 'today' })
    Object.assign(serviceData, data)
  }
  catch (error) {
    console.error('Failed to load service data:', error)
  }
}

// 加载最近应用
async function loadRecentApplications() {
  try {
    const res = await appApi.getList({
      page_size: 5,
    })
    recentApplications.value = res.data ?? []
  }
  catch (error) {
    console.error('Failed to load recent applications:', error)
  }
}

// 刷新数据
async function refreshData() {
  loading.value = true
  try {
    await Promise.all([
      loadStats(),
      loadTrendData(),
      loadServiceData(),
      loadRecentApplications(),
    ])
    toast.success('数据已刷新')
  }
  catch (error) {
    toast.error('刷新失败，请重试')
  }
  finally {
    loading.value = false
  }
}

// 初始化数据
onMounted(() => {
  refreshData()
})

// 自定义时间段对话框（替代原生 prompt）
function openCustomPeriodDialog() {
  customPeriodForm.start_date = ''
  customPeriodForm.end_date = ''
  showCustomPeriodDialog.value = true
}

// 确认自定义时间段
async function confirmCustomPeriod() {
  if (!customPeriodForm.start_date || !customPeriodForm.end_date) {
    toast.error('请选择开始和结束日期')
    return
  }

  try {
    // 这里可以调用自定义时间段的数据获取 API
    toast.success('已应用自定义时间段')
    showCustomPeriodDialog.value = false
    await loadTrendData()
  }
  catch (error) {
    toast.error('获取自定义时间段数据失败')
  }
}
</script>

<template>
  <ClientLayout>
    <div class="space-y-6">
      <!-- 页面标题 -->
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">
            控制台
          </h1>
          <p class="text-gray-600">
            欢迎回来，{{ userStore.user?.name || '用户' }}！
          </p>
        </div>
        <div class="flex items-center space-x-3">
          <Button
            :disabled="loading"
            variant="outline"
            @click="refreshData"
          >
            <RefreshCw
              class="w-4 h-4"
              :class="[{ 'animate-spin': loading }]"
            />
            刷新
          </Button>
          <Button as-child>
            <RouterLink to="/applications/create">
              <Plus class="w-4 h-4" />
              创建应用
            </RouterLink>
          </Button>
        </div>
      </div>

      <!-- 统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card class="px-0 py-4">
          <CardContent>
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                  <Zap class="w-5 h-5 text-white" />
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <div class="text-sm font-medium text-muted-foreground truncate">
                  今日费用
                </div>
                <div class="text-lg font-medium">
                  {{ formatNumber(stats.today_cost) }}
                </div>
              </div>
            </div>
            <div class="mt-4 pt-4 border-t">
              <div class="text-sm">
                <Badge
                  variant="secondary"
                  class="text-green-600"
                >
                  +{{ stats.today_cost_growth }}%
                </Badge>
                <span class="text-muted-foreground ml-2">较昨日</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card class="px-0 py-4">
          <CardContent>
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                  <DollarSign class="w-5 h-5 text-white" />
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <div class="text-sm font-medium text-muted-foreground truncate">
                  本月费用
                </div>
                <div class="text-lg font-medium">
                  ¥{{ formatCurrency(stats.monthly_cost) }}
                </div>
              </div>
            </div>
            <div class="mt-4 pt-4 border-t">
              <div class="text-sm">
                <Badge
                  variant="secondary"
                  class="text-green-600"
                >
                  +{{ stats.monthly_cost_growth }}%
                </Badge>
                <span class="text-muted-foreground ml-2">较上月</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card class="px-0 py-4">
          <CardContent>
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                  <DollarSign class="w-5 h-5 text-white" />
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <div class="text-sm font-medium text-muted-foreground truncate">
                  本月日均费用
                </div>
                <div class="text-lg font-medium">
                  ¥{{ formatCurrency(stats.daily_average_cost) }}
                </div>
              </div>
            </div>
            <div class="mt-4 pt-4 border-t">
              <div class="text-sm text-muted-foreground">
                平均单次请求费用：¥{{ formatCurrency(stats.avg_cost_per_call) }}(本月)
              </div>
            </div>
          </CardContent>
        </Card>

        <Card class="px-0 py-4">
          <CardContent>
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                  <Layers class="w-5 h-5 text-white" />
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <div class="text-sm font-medium text-muted-foreground truncate">
                  应用数量
                </div>
                <div class="text-lg font-medium">
                  {{ stats.app_count }}
                </div>
              </div>
            </div>
            <div class="mt-4 pt-4 border-t">
              <div class="text-sm">
                <RouterLink
                  to="/applications"
                  class="text-primary hover:text-primary/80 font-medium"
                >
                  查看全部
                </RouterLink>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- 图表区域 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 使用趋势图 -->
        <Card>
          <CardHeader>
            <div class="flex items-center justify-between">
              <CardTitle>使用趋势</CardTitle>
              <Select
                v-model="trendPeriod"
                @update:model-value="(value) => {
                  if (value === 'custom') {
                    openCustomPeriodDialog()
                  }
                  else {
                    loadTrendData()
                  }
                }"
              >
                <SelectTrigger class="w-fit">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7d">
                    最近7天
                  </SelectItem>
                  <SelectItem value="30d">
                    最近30天
                  </SelectItem>
                  <SelectItem value="90d">
                    最近90天
                  </SelectItem>
                  <SelectItem value="custom">
                    自定义时间段
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardHeader>
          <CardContent>
            <div class="h-64">
              <Line
                v-if="trendData.labels.length > 0"
                :data="trendChartData"
                :options="trendChartOptions"
              />
              <div
                v-else
                class="flex items-center justify-center h-full text-gray-500"
              >
                暂无数据
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- 服务分布图 -->
        <Card>
          <CardHeader>
            <CardTitle>今日计费服务使用分布</CardTitle>
            <CardDescription>各类AI服务的费用使用占比</CardDescription>
          </CardHeader>
          <CardContent>
            <div class="h-64">
              <Doughnut
                v-if="serviceData.labels.length > 0"
                :data="serviceChartData"
                :options="serviceChartOptions"
              />
              <div
                v-else
                class="flex items-center justify-center h-full text-gray-500"
              >
                暂无数据
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- 最近活动 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 最近应用 -->
        <Card>
          <CardHeader>
            <div class="flex items-center justify-between">
              <CardTitle>最近应用</CardTitle>
              <Button
                variant="outline"
                size="sm"
                as-child
              >
                <RouterLink to="/applications">
                  查看全部
                </RouterLink>
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div
              v-if="recentApplications.length === 0"
              class="text-center py-8"
            >
              <Layers class="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 class="mt-2 text-sm font-medium">
                暂无应用
              </h3>
              <p class="mt-1 text-sm text-muted-foreground">
                创建您的第一个应用开始使用AI服务
              </p>
              <div class="mt-6">
                <Button as-child>
                  <RouterLink to="/applications/create">
                    <Plus class="w-4 h-4" />
                    创建应用
                  </RouterLink>
                </Button>
              </div>
            </div>

            <div
              v-else
              class="space-y-4"
            >
              <div
                v-for="app in recentApplications"
                :key="app.id"
                class="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
              >
                <div class="flex items-center space-x-3">
                  <div class="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                    <Layers class="w-5 h-5 text-primary" />
                  </div>
                  <div>
                    <h4 class="font-medium">
                      {{ app.name }}
                    </h4>
                    <p class="text-sm text-muted-foreground">
                      {{ app.description }}
                    </p>
                  </div>
                </div>
                <div class="text-right">
                  <div class="text-sm font-medium">
                    {{ formatNumber(app.stats?.month_cost || 0) }}
                  </div>
                  <div class="text-xs text-muted-foreground">
                    本月费用
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- 快速操作 -->
        <Card>
          <CardHeader>
            <CardTitle>快速操作</CardTitle>
            <CardDescription>常用功能快速入口</CardDescription>
          </CardHeader>
          <CardContent>
            <div class="grid grid-cols-2 gap-4">
              <Button
                variant="outline"
                class="h-20 flex-col"
                as-child
              >
                <RouterLink to="/applications/create">
                  <Plus class="w-6 h-6 mb-2" />
                  创建应用
                </RouterLink>
              </Button>
              <Button
                variant="outline"
                class="h-20 flex-col"
                as-child
              >
                <RouterLink to="/billing">
                  <BarChart3 class="w-6 h-6 mb-2" />
                  查看账单
                </RouterLink>
              </Button>
              <Button
                variant="outline"
                class="h-20 flex-col"
                as-child
              >
                <RouterLink to="/docs">
                  <FileText class="w-6 h-6 mb-2" />
                  API文档
                </RouterLink>
              </Button>
              <Button
                variant="outline"
                class="h-20 flex-col"
                as-child
              >
                <RouterLink to="/help">
                  <HelpCircle class="w-6 h-6 mb-2" />
                  帮助中心
                </RouterLink>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>

    <!-- 自定义时间段对话框 -->
    <Dialog
      :open="showCustomPeriodDialog"
      @update:open="(open) => showCustomPeriodDialog = open"
    >
      <DialogContent>
        <DialogHeader>
          <DialogTitle>自定义时间段</DialogTitle>
          <DialogDescription>
            选择您要查看数据的时间范围
          </DialogDescription>
        </DialogHeader>
        <div class="space-y-4">
          <div>
            <Label class="text-sm font-medium mb-2">
              开始日期
            </Label>
            <Input
              v-model="customPeriodForm.start_date"
              type="date"
              required
            />
          </div>
          <div>
            <Label class="text-sm font-medium mb-2">
              结束日期
            </Label>
            <Input
              v-model="customPeriodForm.end_date"
              type="date"
              required
            />
          </div>
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            @click="showCustomPeriodDialog = false"
          >
            取消
          </Button>
          <Button @click="confirmCustomPeriod">
            确认
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    <!-- 快速操作对话框 -->
    <Dialog
      :open="showQuickActionDialog"
      @update:open="(open) => showQuickActionDialog = open"
    >
      <DialogContent>
        <DialogHeader>
          <DialogTitle>快速操作</DialogTitle>
          <DialogDescription>
            选择您要执行的操作
          </DialogDescription>
        </DialogHeader>
        <div class="grid grid-cols-2 gap-4">
          <Button
            variant="outline"
            class="h-20 flex-col"
            @click="() => {
              showQuickActionDialog = false
              $router.push('/applications/create')
            }"
          >
            <Plus class="w-6 h-6 mb-2" />
            创建应用
          </Button>
          <Button
            variant="outline"
            class="h-20 flex-col"
            @click="() => {
              showQuickActionDialog = false
              refreshData()
            }"
          >
            <RefreshCw class="w-6 h-6 mb-2" />
            刷新数据
          </Button>
          <Button
            variant="outline"
            class="h-20 flex-col"
            @click="() => {
              showQuickActionDialog = false
              openCustomPeriodDialog()
            }"
          >
            <BarChart3 class="w-6 h-6 mb-2" />
            自定义报表
          </Button>
          <Button
            variant="outline"
            class="h-20 flex-col"
            @click="() => {
              showQuickActionDialog = false
              $router.push('/billing')
            }"
          >
            <DollarSign class="w-6 h-6 mb-2" />
            查看账单
          </Button>
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            @click="showQuickActionDialog = false"
          >
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </ClientLayout>
</template>
