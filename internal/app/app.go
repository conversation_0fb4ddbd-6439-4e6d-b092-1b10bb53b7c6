package app

import (
	"context"
	"fmt"

	"git.uozi.org/uozi/potato-billing-api/internal/billing"
	"git.uozi.org/uozi/potato-billing-api/model"
	"git.uozi.org/uozi/potato-billing-api/query"
	"github.com/uozi-tech/cosy"
	"github.com/uozi-tech/cosy/logger"
)

// CreateAppRequest 创建应用请求结构
type CreateAppRequest struct {
	Name    string `json:"name" binding:"required"`
	Comment string `json:"comment"`
}

// UpdateAppRequest 更新应用请求结构
type UpdateAppRequest struct {
	Name    string `json:"name"`
	Comment string `json:"comment"`
	Status  string `json:"status"`
}

// AppStatusResponse 应用状态响应结构
type AppStatusResponse struct {
	AppID     uint64                      `json:"app_id"`
	Status    string                      `json:"status"`
	Available bool                        `json:"available"`
	Quotas    []*model.QuotaPackageRecord `json:"quotas"`
	User      *model.User                 `json:"user,omitempty"`
	CreatedAt int64                       `json:"created_at"`
	UpdatedAt int64                       `json:"updated_at"`
}

// AppListQuery 应用列表查询参数
type AppListQuery struct {
	UserID   uint64 `form:"user_id"`
	Search   string `form:"search"`
	Status   string `form:"status"`
	Page     int    `form:"page"`
	PageSize int    `form:"pageSize"`
}

// AppStats 应用统计信息
type AppStats struct {
	TotalRequests   int64   `json:"total_requests"`
	TodayRequests   int64   `json:"today_requests"`
	TotalCost       float64 `json:"total_cost"`
	TodayCost       float64 `json:"today_cost"`
	LastRequestTime int64   `json:"last_request_time"`
}

// AppWithStats 带统计信息的应用
type AppWithStats struct {
	*model.App
	Stats AppStats `json:"stats"`
}

// AppListResponse 应用列表响应
type AppListResponse struct {
	Data       []AppWithStats         `json:"data"`
	Pagination map[string]interface{} `json:"pagination"`
}

// GetAppList 获取应用列表
func GetAppList(ctx context.Context, queryParams *AppListQuery) (*AppListResponse, error) {
	db := cosy.UseDB(ctx)
	q := query.Use(db).App

	queryBuilder := q.Select(q.ALL)

	// 添加用户过滤
	if queryParams.UserID > 0 {
		queryBuilder = queryBuilder.Where(q.UserID.Eq(queryParams.UserID))
	}

	// 添加搜索条件
	if queryParams.Search != "" {
		queryBuilder = queryBuilder.Where(
			q.Name.Like("%" + queryParams.Search + "%"),
		).Or(
			q.Comment.Like("%" + queryParams.Search + "%"),
		)
	}

	// 添加状态过滤
	if queryParams.Status != "" {
		queryBuilder = queryBuilder.Where(q.Status.Eq(queryParams.Status))
	}

	// 获取总数
	total, err := queryBuilder.Count()
	if err != nil {
		return nil, fmt.Errorf("获取应用总数失败: %w", err)
	}

	// 设置分页参数
	if queryParams.Page < 1 {
		queryParams.Page = 1
	}
	if queryParams.PageSize < 1 || queryParams.PageSize > 100 {
		queryParams.PageSize = 20
	}

	// 分页查询
	offset := (queryParams.Page - 1) * queryParams.PageSize
	apps, err := queryBuilder.
		Preload(q.User).
		Order(q.CreatedAt.Desc()).
		Limit(queryParams.PageSize).
		Offset(offset).
		Find()
	if err != nil {
		return nil, fmt.Errorf("获取应用列表失败: %w", err)
	}

	// 为每个应用添加统计数据
	appsWithStats := make([]AppWithStats, 0, len(apps))
	for _, app := range apps {
		stats := getAppStats(ctx, app.ID)
		appWithStats := AppWithStats{
			App:   app,
			Stats: stats,
		}
		appsWithStats = append(appsWithStats, appWithStats)
	}

	// 构建响应
	response := &AppListResponse{
		Data: appsWithStats,
		Pagination: map[string]interface{}{
			"total":        total,
			"per_page":     queryParams.PageSize,
			"current_page": queryParams.Page,
			"total_pages":  (total + int64(queryParams.PageSize) - 1) / int64(queryParams.PageSize),
		},
	}

	return response, nil
}

// GetApp 获取应用详情
func GetApp(ctx context.Context, appID uint64, userID uint64) (*model.App, error) {
	q := query.App

	queryBuilder := q.Preload(q.User).Where(q.ID.Eq(appID))

	// 如果指定了用户ID，添加用户过滤
	if userID > 0 {
		queryBuilder = queryBuilder.Where(q.UserID.Eq(userID))
	}

	app, err := queryBuilder.First()
	if err != nil {
		return nil, fmt.Errorf("获取应用失败: %w", err)
	}

	return app, nil
}

// CreateApp 创建应用
func CreateApp(ctx context.Context, userID uint64, req *CreateAppRequest) (*model.App, error) {
	// 创建新的应用记录
	app := &model.App{
		Name:    req.Name,
		Status:  "ok",
		UserID:  userID,
		Comment: req.Comment,
	}

	// 调用 billing 服务创建应用
	billingService := billing.GetBillingService()
	err := billingService.GetAppService().CreateApp(ctx, app)
	if err != nil {
		return nil, fmt.Errorf("创建应用失败: %w", err)
	}

	// 重新查询以获取完整信息
	result, err := query.App.
		Preload(query.App.User).
		Where(query.App.ID.Eq(app.ID)).
		First()
	if err != nil {
		return nil, fmt.Errorf("获取创建的应用失败: %w", err)
	}

	return result, nil
}

// UpdateApp 更新应用
func UpdateApp(ctx context.Context, appID uint64, userID uint64, req *UpdateAppRequest) (*model.App, error) {
	q := query.App

	queryBuilder := q.Where(q.ID.Eq(appID))

	// 如果指定了用户ID，添加用户过滤
	if userID > 0 {
		queryBuilder = queryBuilder.Where(q.UserID.Eq(userID))
	}

	// 检查应用是否存在
	_, err := queryBuilder.First()
	if err != nil {
		return nil, fmt.Errorf("应用不存在或无权限: %w", err)
	}

	// 构建更新数据
	updates := make(map[string]interface{})
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.Comment != "" {
		updates["comment"] = req.Comment
	}
	if req.Status != "" {
		updates["status"] = req.Status
	}

	// 更新应用信息
	_, err = queryBuilder.Updates(updates)
	if err != nil {
		return nil, fmt.Errorf("更新应用失败: %w", err)
	}

	// 返回更新后的数据
	result, err := q.Preload(q.User).Where(q.ID.Eq(appID)).First()
	if err != nil {
		return nil, fmt.Errorf("获取更新后的应用失败: %w", err)
	}

	return result, nil
}

// DeleteApp 删除应用
func DeleteApp(ctx context.Context, appID uint64, userID uint64) error {
	q := query.App

	queryBuilder := q.Where(q.ID.Eq(appID))

	// 如果指定了用户ID，添加用户过滤
	if userID > 0 {
		queryBuilder = queryBuilder.Where(q.UserID.Eq(userID))
	}

	// 检查应用是否存在
	_, err := queryBuilder.First()
	if err != nil {
		return fmt.Errorf("应用不存在或无权限: %w", err)
	}

	// 删除应用
	_, err = queryBuilder.Delete()
	if err != nil {
		return fmt.Errorf("删除应用失败: %w", err)
	}

	return nil
}

// RegenerateAPIKey 重新生成API Key
func RegenerateAPIKey(ctx context.Context, appID uint64, userID uint64) (*model.App, error) {
	q := query.App

	queryBuilder := q.Where(q.ID.Eq(appID))

	// 如果指定了用户ID，添加用户过滤
	if userID > 0 {
		queryBuilder = queryBuilder.Where(q.UserID.Eq(userID))
	}

	// 检查应用是否存在
	_, err := queryBuilder.First()
	if err != nil {
		return nil, fmt.Errorf("应用不存在或无权限: %w", err)
	}

	// 生成新的API Key
	app := &model.App{}
	newAPIKey := app.GenerateAPIKey()

	_, err = queryBuilder.Update(q.APIKey, newAPIKey)
	if err != nil {
		return nil, fmt.Errorf("更新API Key失败: %w", err)
	}

	// 返回更新后的数据
	result, err := q.Preload(q.User).Where(q.ID.Eq(appID)).First()
	if err != nil {
		return nil, fmt.Errorf("获取更新后的应用失败: %w", err)
	}

	return result, nil
}

// GetAppStatus 获取应用状态
func GetAppStatus(ctx context.Context, appID uint64) (*AppStatusResponse, error) {
	// 查询应用记录
	app, err := query.App.
		Preload(query.App.User).
		Where(query.App.ID.Eq(appID)).
		First()
	if err != nil {
		return nil, fmt.Errorf("获取应用失败: %w", err)
	}

	// 查询该应用的专属配额信息
	var quotas []*model.QuotaPackageRecord
	err = query.QuotaPackageRecord.UnderlyingDB().
		Where("(api_key IN ? OR api_key IS NULL)", []string{app.APIKey, ""}).
		Where("user_id = ?", app.UserID).
		Find(&quotas).Error
	if err != nil {
		logger.Error("Failed to query quotas", "error", err, "app_id", app.ID)
		return nil, fmt.Errorf("查询配额失败: %w", err)
	}

	// 计算是否可用（只检查对应类型的配额）
	available := app.Status == "ok"
	if len(quotas) > 0 {
		for _, quota := range quotas {
			if quota.Quota > 0 && quota.Used >= quota.Quota && app.User.Balance <= 0 {
				available = false
				break
			}
		}
	} else {
		// 没有该类型的配额，检查用户余额
		if app.User == nil || app.User.Balance <= 0 {
			available = false
		}
	}

	response := &AppStatusResponse{
		AppID:     app.ID,
		Status:    app.Status,
		Available: available,
		Quotas:    quotas,
		User:      app.User,
		CreatedAt: app.CreatedAt,
		UpdatedAt: app.UpdatedAt,
	}

	return response, nil
}

// GetAppOverviewStats 获取应用概览统计
func GetAppOverviewStats(ctx context.Context) (interface{}, error) {
	// 获取统计服务实例
	statService := billing.GetStatServiceInstance()
	if statService == nil {
		return nil, fmt.Errorf("统计服务不可用")
	}

	// 调用统计服务获取数据
	stats, err := statService.GetAppOverviewStats(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取应用概览统计失败: %w", err)
	}

	return stats, nil
}

// getAppStats 获取应用统计信息
func getAppStats(ctx context.Context, appID uint64) AppStats {
	// 这里应该调用统计服务获取应用的统计数据
	// 暂时返回空的统计数据
	return AppStats{
		TotalRequests:   0,
		TodayRequests:   0,
		TotalCost:       0,
		TodayCost:       0,
		LastRequestTime: 0,
	}
}
