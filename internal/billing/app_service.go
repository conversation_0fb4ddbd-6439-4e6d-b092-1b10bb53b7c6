package billing

import (
	"context"
	"fmt"
	"time"

	"git.uozi.org/uozi/potato-billing-api/model"
	"git.uozi.org/uozi/potato-billing-api/query"
	"git.uozi.org/uozi/potato-billing-api/settings"
	"git.uozi.org/uozi/potato-billing-api/types"
	"github.com/uozi-tech/cosy/logger"
)

// AppService Key状态管理服务
type AppService struct {
	cacheService *CacheService
	mqttService  *MQTTService
	config       *settings.BillingConfig
}

// NewAppService 创建应用管理服务
func NewAppService(cacheService *CacheService, mqttService *MQTTService, config *settings.BillingConfig) *AppService {
	return &AppService{
		cacheService: cacheService,
		mqttService:  mqttService,
		config:       config,
	}
}

// GetAppStatusWithCache 带缓存的Key状态查询
func (as *AppService) GetAppStatusWithCache(appID uint64) (*CachedKeyStatus, error) {
	// 先从缓存获取
	status, err := as.cacheService.GetAppStatus(appID)
	if err != nil {
		logger.Error("从缓存获取应用状态失败", "error", err, "app_id", appID)
	}

	if status != nil {
		// 检查缓存是否过期
		if time.Now().Unix()-status.UpdatedAt < int64(as.config.CacheTTL.Seconds()) {
			return status, nil
		}
	}

	// 从数据库获取
	billingKey, err := query.App.
		Preload(query.App.User).
		Where(query.App.ID.Eq(appID)).
		First()
	if err != nil {
		return nil, err
	}

	quotas, err := query.QuotaPackageRecord.
		Where(query.QuotaPackageRecord.AppID.Eq(billingKey.ID)).
		Find()
	if err != nil {
		return nil, err
	}

	// 计算是否可用
	available := billingKey.Status == types.AppStatusOk
	for _, quota := range quotas {
		if quota.Quota > 0 && quota.Used >= quota.Quota {
			available = false
			break
		}
	}

	// 构建缓存对象
	cachedStatus := &CachedKeyStatus{
		AppID:     billingKey.ID,
		Status:    billingKey.Status,
		Available: available,
		Quotas:    quotas,
		UpdatedAt: time.Now().Unix(),
	}

	// 更新缓存
	if err := as.cacheService.SetAppStatus(cachedStatus, as.config.CacheTTL); err != nil {
		logger.Error("更新应用状态缓存失败", "error", err, "app_id", appID)
	}

	return cachedStatus, nil
}

// UpdateAppStatusBatch 批量更新Key状态并推送消息
func (as *AppService) UpdateAppStatusBatch(updates []AppStatusUpdate) error {
	if len(updates) == 0 {
		return nil
	}

	// 提取所有需要更新的应用
	appIDs := make([]uint64, len(updates))
	for i, update := range updates {
		appIDs[i] = update.AppID
	}

	// 批量更新数据库状态
	status := updates[0].Status
	_, err := query.App.
		Where(query.App.ID.In(appIDs...)).
		Update(query.App.Status, status)
	if err != nil {
		return fmt.Errorf("更新Key状态失败: %w", err)
	}

	apps, err := query.App.Where(query.App.ID.In(appIDs...)).Find()
	if err != nil {
		return fmt.Errorf("获取应用失败: %w", err)
	}

	logger.Info("批量Key状态更新成功", "count", len(updates), "status", status)

	// 批量清除缓存
	for _, app := range apps {
		if err := as.cacheService.InvalidateAppStatus(app.ID); err != nil {
			logger.Error("清除应用状态缓存失败", "error", err, "app_id", app.ID)
		}
	}

	// 批量发布状态更新
	as.mqttService.PublishAppStatusBatch(updates)

	return nil
}

// CheckAndRestoreAppStatus 检查并恢复Key状态（用于充值和添加资源包后）
func (as *AppService) CheckAndRestoreAppStatus(ctx context.Context, userID uint64, appID uint64, module string) error {
	// 查询被阻塞的Key
	q := query.App.Where(query.App.UserID.Eq(userID))

	// 如果指定了appID，只处理该应用
	if appID != 0 {
		q = q.Where(query.App.ID.Eq(appID))
	} else {
		// 如果未指定appID，则检查所有被阻塞的Key
		q = q.Where(query.App.Status.Eq(types.AppStatusBlocked))
	}

	blockedApps, err := q.Find()
	if err != nil {
		return fmt.Errorf("查询被阻塞的Key失败: %w", err)
	}

	if len(blockedApps) == 0 {
		return nil // 没有被阻塞的Key
	}

	var appsToRestore []AppStatusUpdate

	for _, app := range blockedApps {
		canRestore := false

		// 检查该Key的配额状态
		quotaQuery := query.QuotaPackageRecord.Where(
			query.QuotaPackageRecord.AppID.Eq(app.ID),
			query.QuotaPackageRecord.Status.Eq(types.QuotaPackageStatusActive))

		// 如果指定了模块，只检查该模块
		if module != "" {
			quotaQuery = quotaQuery.Where(query.QuotaPackageRecord.Module.Eq(module))
		}

		quotas, err := quotaQuery.Find()
		if err != nil {
			logger.Error("查询配额失败", "error", err, "app", app.APIKey)
			continue
		}

		// 检查是否有可用的配额
		for _, quota := range quotas {
			if quota.Quota > quota.Used {
				canRestore = true
				break
			}
		}

		// 如果没有足够的配额，检查用户余额
		if !canRestore {
			user, err := query.User.Where(query.User.ID.Eq(userID)).First()
			if err != nil {
				logger.Error("查询用户失败", "error", err, "userID", userID)
				continue
			}

			// 如果用户有余额，也可以恢复
			if user.Balance > 0 {
				canRestore = true
			}
		}

		if canRestore {
			appsToRestore = append(appsToRestore, AppStatusUpdate{
				AppID:  app.ID,
				APIKey: app.APIKey,
				Status: types.AppStatusOk,
				Reason: "余额充值或配额补充",
			})
		}
	}

	// 批量恢复Key状态
	if len(appsToRestore) > 0 {
		err = as.UpdateAppStatusBatch(appsToRestore)
		if err != nil {
			return fmt.Errorf("批量恢复应用状态失败: %w", err)
		}

		logger.Info("成功恢复应用状态", "count", len(appsToRestore), "userID", userID)
	}

	return nil
}

// CreateApp 创建应用
func (as *AppService) CreateApp(ctx context.Context, app *model.App) error {
	// 生成API Key
	app.GenerateAPIKey()

	err := query.App.Create(app)
	if err != nil {
		return fmt.Errorf("创建应用失败: %w", err)
	}

	as.CheckAndRestoreAppStatus(ctx, app.UserID, app.ID, "")

	return nil
}
