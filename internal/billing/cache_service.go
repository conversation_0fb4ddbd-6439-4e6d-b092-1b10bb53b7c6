package billing

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"git.uozi.org/uozi/potato-billing-api/model"
	"github.com/redis/go-redis/v9"
	"github.com/uozi-tech/cosy/logger"
	cosyRedis "github.com/uozi-tech/cosy/redis"
)

// CacheService Redis缓存服务
type CacheService struct {
	client *redis.Client
}

var (
	cacheServiceInstance *CacheService
	cacheServiceOnce     sync.Once
)

// CachedKeyStatus 缓存的Key状态信息
type CachedKeyStatus struct {
	AppID     uint64                      `json:"app_id"`
	Status    string                      `json:"status"`
	Available bool                        `json:"available"`
	Quotas    []*model.QuotaPackageRecord `json:"quotas"`
	UpdatedAt int64                       `json:"updated_at"`
}

// GetCacheService 获取缓存服务单例
func GetCacheService() *CacheService {
	cacheServiceOnce.Do(func() {
		redisClient := cosyRedis.GetClient()
		cacheServiceInstance = &CacheService{
			client: redisClient,
		}
		logger.Info("CacheService singleton initialized")
	})
	return cacheServiceInstance
}

// GetAppStatus 从缓存获取App状态
func (c *CacheService) GetAppStatus(appID uint64) (*CachedKeyStatus, error) {
	cacheKey := c.buildAppStatusCacheKey(appID)

	result, err := c.client.Get(context.Background(), cacheKey).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil // 缓存未命中
		}
		logger.Error("从Redis获取应用状态失败", "error", err, "app_id", appID)
		return nil, err
	}

	var status CachedKeyStatus
	if err := json.Unmarshal([]byte(result), &status); err != nil {
		logger.Error("反序列化应用状态失败", "error", err, "app_id", appID)
		return nil, err
	}

	return &status, nil
}

// SetAppStatus 设置App状态到缓存
func (c *CacheService) SetAppStatus(status *CachedKeyStatus, ttl time.Duration) error {
	cacheKey := c.buildAppStatusCacheKey(status.AppID)

	data, err := json.Marshal(status)
	if err != nil {
		logger.Error("序列化应用状态失败", "error", err, "app_id", status.AppID)
		return err
	}

	err = c.client.Set(context.Background(), cacheKey, data, ttl).Err()
	if err != nil {
		logger.Error("设置应用状态到Redis失败", "error", err, "app_id", status.AppID)
		return err
	}

	logger.Debug("应用状态缓存成功", "app_id", status.AppID, "ttl", ttl)
	return nil
}

// InvalidateAppStatus 使App状态缓存失效
func (c *CacheService) InvalidateAppStatus(appID uint64) error {
	cacheKey := c.buildAppStatusCacheKey(appID)

	err := c.client.Del(context.Background(), cacheKey).Err()
	if err != nil {
		logger.Error("删除应用状态缓存失败", "error", err, "app_id", appID)
		return err
	}

	logger.Debug("应用状态缓存已删除", "app_id", appID)
	return nil
}

// GetQuotaUsage 获取配额使用情况
func (c *CacheService) GetQuotaUsage(ctx context.Context, appID uint64, module string) (int64, error) {
	cacheKey := c.buildQuotaUsageCacheKey(appID, module)

	result, err := c.client.Get(ctx, cacheKey).Result()
	if err != nil {
		if err == redis.Nil {
			return 0, nil // 缓存未命中，返回0
		}
		logger.Error("从Redis获取配额使用情况失败", "error", err, "app_id", appID, "module", module)
		return 0, err
	}

	var used int64
	if err := json.Unmarshal([]byte(result), &used); err != nil {
		logger.Error("反序列化配额使用情况失败", "error", err, "app_id", appID, "module", module)
		return 0, err
	}

	return used, nil
}

// IncrQuotaUsage 增加配额使用量
func (c *CacheService) IncrQuotaUsage(ctx context.Context, appID uint64, module string, tokens int64, ttl time.Duration) (int64, error) {
	cacheKey := c.buildQuotaUsageCacheKey(appID, module)

	result := c.client.IncrBy(ctx, cacheKey, tokens)
	if err := result.Err(); err != nil {
		logger.Error("增加配额使用量失败", "error", err, "app_id", appID, "module", module, "tokens", tokens)
		return 0, err
	}

	// 设置过期时间
	c.client.Expire(ctx, cacheKey, ttl)

	newUsed := result.Val()
	logger.Debug("配额使用量增加成功", "app_id", appID, "module", module, "tokens", tokens, "new_used", newUsed)

	return newUsed, nil
}

// SetQuotaUsage 设置配额使用量
func (c *CacheService) SetQuotaUsage(ctx context.Context, appID uint64, module string, used int64, ttl time.Duration) error {
	cacheKey := c.buildQuotaUsageCacheKey(appID, module)

	err := c.client.Set(ctx, cacheKey, used, ttl).Err()
	if err != nil {
		logger.Error("设置配额使用量到Redis失败", "error", err, "app_id", appID, "module", module, "used", used)
		return err
	}

	logger.Debug("配额使用量设置成功", "app_id", appID, "module", module, "used", used)
	return nil
}

// InvalidateQuotaUsage 使配额使用量缓存失效
func (c *CacheService) InvalidateQuotaUsage(appID uint64, module string) error {
	cacheKey := c.buildQuotaUsageCacheKey(appID, module)

	err := c.client.Del(context.Background(), cacheKey).Err()
	if err != nil {
		logger.Error("删除配额使用量缓存失败", "error", err, "app_id", appID, "module", module)
		return err
	}

	logger.Debug("配额使用量缓存已删除", "app_id", appID, "module", module)
	return nil
}

// GetPricingRules 获取缓存的价格规则
func (c *CacheService) GetPricingRules(module, modelName string) (*model.PricingRule, error) {
	cacheKey := c.buildPricingRuleCacheKey(module, modelName)

	result, err := c.client.Get(context.Background(), cacheKey).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil // 缓存未命中
		}
		logger.Error("从Redis获取价格规则失败", "error", err, "module", module, "model", modelName)
		return nil, err
	}

	var rule model.PricingRule
	if err := json.Unmarshal([]byte(result), &rule); err != nil {
		logger.Error("反序列化价格规则失败", "error", err, "module", module, "model", modelName)
		return nil, err
	}

	return &rule, nil
}

// SetPricingRules 设置价格规则到缓存
func (c *CacheService) SetPricingRules(rule *model.PricingRule, ttl time.Duration) error {
	cacheKey := c.buildPricingRuleCacheKey(rule.Module, rule.ModelName)

	data, err := json.Marshal(rule)
	if err != nil {
		logger.Error("序列化价格规则失败", "error", err, "module", rule.Module, "model", rule.ModelName)
		return err
	}

	err = c.client.Set(context.Background(), cacheKey, data, ttl).Err()
	if err != nil {
		logger.Error("设置价格规则到Redis失败", "error", err, "module", rule.Module, "model", rule.ModelName)
		return err
	}

	logger.Debug("价格规则缓存成功", "module", rule.Module, "model", rule.ModelName, "ttl", ttl)
	return nil
}

// InvalidatePricingRules 使价格规则缓存失效
func (c *CacheService) InvalidatePricingRules(module, modelName string) error {
	cacheKey := c.buildPricingRuleCacheKey(module, modelName)

	err := c.client.Del(context.Background(), cacheKey).Err()
	if err != nil {
		logger.Error("删除价格规则缓存失败", "error: ", err, "module: ", module, "model: ", modelName)
		return err
	}

	logger.Debug("价格规则缓存已删除", "module: ", module, "model: ", modelName)
	return nil
}

// InvalidateAllAppRelated 清除与指定App相关的所有缓存
func (c *CacheService) InvalidateAllAppRelated(ctx context.Context, appID uint64) error {
	// 清除Key状态缓存
	if err := c.InvalidateAppStatus(appID); err != nil {
		logger.Error("清除应用状态缓存失败", "error: ", err, "app_id: ", appID)
	}

	// 清除所有模块的配额使用量缓存
	modules := []string{"llm", "tts", "asr"} // 支持的模块类型
	for _, module := range modules {
		if err := c.InvalidateQuotaUsage(appID, module); err != nil {
			logger.Error("清除配额使用量缓存失败", "error: ", err, "app_id: ", appID, "module: ", module)
		}
	}

	logger.Info("应用相关缓存已清除", "app_id: ", appID)
	return nil
}

// buildAppStatusCacheKey 构建App状态缓存键
func (c *CacheService) buildAppStatusCacheKey(appID uint64) string {
	return fmt.Sprintf("billing:app_status:%d", appID)
}

// buildQuotaUsageCacheKey 构建配额使用量缓存键
func (c *CacheService) buildQuotaUsageCacheKey(appID uint64, module string) string {
	return fmt.Sprintf("billing:quota_usage:%d:%s", appID, module)
}

// buildPricingRuleCacheKey 构建价格规则缓存键
func (c *CacheService) buildPricingRuleCacheKey(module, modelName string) string {
	if modelName == "" {
		return fmt.Sprintf("billing:pricing_rule:%s:default", module)
	}
	return fmt.Sprintf("billing:pricing_rule:%s:%s", module, modelName)
}

// CheckConnection 检查Redis连接
func (c *CacheService) CheckConnection(ctx context.Context) error {
	_, err := c.client.Ping(ctx).Result()
	if err != nil {
		logger.Error("Redis连接检查失败", "error", err)
		return err
	}
	return nil
}
