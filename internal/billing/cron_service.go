package billing

import (
	"sync"
	"time"

	"git.uozi.org/uozi/potato-billing-api/model"
	"git.uozi.org/uozi/potato-billing-api/query"
	"git.uozi.org/uozi/potato-billing-api/types"
	"github.com/go-co-op/gocron/v2"
	"github.com/shopspring/decimal"
	"github.com/uozi-tech/cosy/logger"
	"gorm.io/gorm"
)

// CronService 定时任务服务
type CronService struct {
	billingService *BillingService
	scheduler      gocron.Scheduler
}

var (
	cronServiceInstance *CronService
	cronServiceOnce     sync.Once
)

// GetCronService 获取定时任务服务单例
func GetCronService() *CronService {
	cronServiceOnce.Do(func() {
		billingService := GetBillingService()

		// 创建调度器
		scheduler, err := gocron.NewScheduler()
		if err != nil {
			logger.Error("创建定时任务调度器失败", "error", err)
			return
		}

		cronServiceInstance = &CronService{
			billingService: billingService,
			scheduler:      scheduler,
		}
		logger.Info("CronService singleton initialized")
	})
	return cronServiceInstance
}

// CheckOverdueKeys 检查欠费的key并停止服务
func (c *CronService) CheckOverdueKeys() {
	logger.Info("开始检查欠费的key...")

	// 查询所有状态为 ok 的 key
	apps, err := query.App.
		Where(query.App.Status.Eq(types.AppStatusOk)).
		Find()
	if err != nil {
		logger.Error("查询billing keys失败", "error", err)
		return
	}

	// 收集需要阻止的keys
	var keysToBlock []AppStatusUpdate

	for _, app := range apps {
		if c.shouldBlockKey(*app) {
			keysToBlock = append(keysToBlock, AppStatusUpdate{
				AppID:  app.ID,
				APIKey: app.APIKey,
				Status: types.AppStatusBlocked,
				Reason: "Quota exhausted or expired",
			})
		}
	}

	if len(keysToBlock) == 0 {
		logger.Info("Overdue key check completed", "total", len(apps), "stopped", 0)
		return
	}

	// 使用批量更新方法
	if err := c.billingService.GetAppService().UpdateAppStatusBatch(keysToBlock); err != nil {
		logger.Error("Failed to update overdue key status", "error", err)
		return
	}

	logger.Info("Overdue key check completed", "total", len(apps), "stopped", len(keysToBlock))
}

// shouldBlockKey 检查key是否应该被阻止
func (c *CronService) shouldBlockKey(app model.App) bool {
	// 获取用户信息
	var user *model.User
	if app.UserID != 0 {
		// 查询用户信息
		foundUser, err := query.User.Where(query.User.ID.Eq(app.UserID)).First()
		if err != nil {
			logger.Error("Failed to query user", "error", err, "user_id", app.UserID)
			// 如果无法获取用户信息，为安全起见阻止key
			return true
		}
		user = foundUser
	}

	var quotas []*model.QuotaPackageRecord
	err := query.QuotaPackageRecord.UnderlyingDB().
		Where(
			"(api_key IN (?) OR api_key IS NULL)",
			[]string{app.APIKey, ""},
		).
		Where("status = ?", types.QuotaPackageStatusActive).
		Find(&quotas).
		Error

	if err != nil {
		logger.Error("Failed to query key quota", "error", err)
		return false
	}

	now := time.Now()
	hasValidQuota := false

	// 检查是否有有效的配额（只检查对应类型的配额）
	if len(quotas) > 0 {
		for _, quota := range quotas {
			// 检查配额是否过期
			if quota.ExpiresAt != 0 && quota.ExpiresAt < now.UnixMilli() {
				logger.Debug("Key quota expired", "key", app.APIKey, "module", quota.Module, "expires_at", quota.ExpiresAt)
				continue
			}

			// 检查配额是否用尽
			if quota.Quota > 0 && quota.Used >= quota.Quota {
				logger.Debug("Key quota exhausted", "key", app.APIKey, "module", quota.Module, "used", quota.Used, "quota", quota.Quota)
				continue
			}

			// 如果有至少一个有效配额，不需要检查余额
			hasValidQuota = true
			break
		}

		// 如果该类型的所有配额都无效（过期或用尽），且用户余额不足，则阻止key
		if !hasValidQuota {
			if user == nil || user.Balance <= 0 {
				logger.Debug("Key blocked: no valid quota for type and insufficient balance",
					"key", app.APIKey,
					"user_id", app.UserID,
					"balance", func() float64 {
						if user != nil {
							return user.Balance
						}
						return 0
					}())
				return true
			}
		}
	} else {
		// 没有该类型的配额设置，检查用户余额
		if user == nil || user.Balance <= 0 {
			logger.Debug("Key blocked: no quota for type and insufficient balance",
				"key", app.APIKey,
				"user_id", app.UserID,
				"balance", func() float64 {
					if user != nil {
						return user.Balance
					}
					return 0
				}())
			return true
		}
	}

	return false
}

// CheckExpiredApps 检查过期的应用
func (c *CronService) CheckExpiredApps() {
	logger.Info("开始检查过期的应用...")

	// 查询所有状态为 ok 的 key
	apps, err := query.App.
		Where(query.App.Status.Eq(types.AppStatusOk)).
		Find()
	if err != nil {
		logger.Error("查询应用失败", "error", err)
		return
	}

	// 收集需要阻止的apps
	var appsToBlock []AppStatusUpdate

	for _, app := range apps {
		if c.shouldBlockKey(*app) {
			appsToBlock = append(appsToBlock, AppStatusUpdate{
				AppID:  app.ID,
				APIKey: app.APIKey,
				Status: types.AppStatusBlocked,
				Reason: "Quota expired and insufficient balance",
			})
		}
	}

	if len(appsToBlock) == 0 {
		logger.Info("应用可用性检查完成", "total", len(apps), "stopped", 0)
		return
	}

	// 使用批量更新方法
	if err := c.billingService.GetAppService().UpdateAppStatusBatch(appsToBlock); err != nil {
		logger.Error("更新应用状态失败", "error", err)
		return
	}

	logger.Info("应用可用性检查完成", "total", len(apps), "stopped", len(appsToBlock))
}

// GenerateHourlyBillingStats 生成每小时账单统计数据
func (c *CronService) GenerateHourlyBillingStats() {
	logger.Info("开始生成每小时账单统计数据...")

	// 获取当前时间和上一小时的时间范围
	now := time.Now()
	currentHour := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), 0, 0, 0, now.Location())
	lastHour := currentHour.Add(-time.Hour)

	// 转换为毫秒时间戳
	startTime := lastHour.UnixMilli()
	endTime := currentHour.UnixMilli()

	logger.Info("统计时间范围", "start_time", lastHour.Format("2006-01-02 15:04:05"), "end_time", currentHour.Format("2006-01-02 15:04:05"))

	// 从usage_logs表中聚合数据
	if err := c.aggregateUsageLogsToBilling(startTime, endTime); err != nil {
		logger.Error("聚合使用日志到账单表失败", "error", err)
		return
	}

	logger.Info("每小时账单统计数据生成完成")
}

// aggregateUsageLogsToBilling 将使用日志聚合到账单表
func (c *CronService) aggregateUsageLogsToBilling(startTime, endTime int64) error {
	// 查询指定时间范围内的使用日志，按用户和模块分组聚合
	var results []struct {
		AppID      uint64  `json:"app_id"`
		UserID     uint64  `json:"user_id"`
		Module     string  `json:"module"`
		TotalUsage int64   `json:"total_usage"`
		TotalCost  float64 `json:"total_cost"`
		TotalCalls int64   `json:"total_calls"`
	}

	err := query.UsageLog.UnderlyingDB().
		Select("app_id, user_id, module, SUM(`usage`) as total_usage, SUM(cost) as total_cost, COUNT(*) as total_calls").
		Where("created_at >= ? AND created_at < ? AND user_id > 0", startTime, endTime).
		Group("app_id, user_id, module").
		Scan(&results).Error

	if err != nil {
		return err
	}

	if len(results) == 0 {
		logger.Info("指定时间范围内没有使用日志数据", "start_time", startTime, "end_time", endTime)
		return nil
	}

	logger.Info("找到聚合数据", "count", len(results))

	// 批量创建或更新账单记录
	for _, result := range results {
		if err := c.createOrUpdateBillingRecord(result.AppID, result.UserID, result.Module, result.TotalUsage, result.TotalCost, result.TotalCalls, startTime); err != nil {
			logger.Error("创建或更新账单记录失败", "error", err, "user_id", result.UserID, "module", result.Module)
			continue
		}
	}

	logger.Info("账单记录处理完成", "processed", len(results))
	return nil
}

// createOrUpdateBillingRecord 创建或更新账单记录
func (c *CronService) createOrUpdateBillingRecord(appID uint64, userID uint64, module string, usage int64, cost float64, calls int64, timestamp int64) error {
	// 检查是否已存在相同时间段的记录
	// 使用时间戳的小时部分作为唯一标识
	hourTimestamp := (timestamp / (60 * 60 * 1000)) * (60 * 60 * 1000) // 向下取整到小时

	existingBilling, err := query.Billing.
		Where(query.Billing.AppID.Eq(appID)).
		Where(query.Billing.UserID.Eq(userID)).
		Where(query.Billing.Module.Eq(module)).
		Where(query.Billing.CreatedAt.Eq(hourTimestamp)).
		First()

	if err != nil && err != gorm.ErrRecordNotFound {
		return err
	}

	if existingBilling != nil {
		// 更新现有记录
		_, err = query.Billing.
			Where(query.Billing.ID.Eq(existingBilling.ID)).
			Updates(map[string]interface{}{
				"usage": usage,
				"cost":  cost,
				"calls": calls,
			})
		if err != nil {
			return err
		}
		logger.Debug("更新账单记录", "app_id", appID, "user_id", userID, "module", module, "usage", usage, "cost", cost, "calls", calls)
	} else {
		// 创建新记录
		newBilling := &model.Billing{
			AppID:  appID,
			UserID: userID,
			Module: module,
			Usage:  usage,
			Calls:  calls,
		}

		// 设置cost字段（使用decimal类型）
		newBilling.Cost = decimal.NewFromFloat(cost)

		// 设置创建时间为小时时间戳
		newBilling.CreatedAt = hourTimestamp
		newBilling.UpdatedAt = time.Now().UnixMilli()

		err = query.Billing.Create(newBilling)
		if err != nil {
			return err
		}
		logger.Debug("创建账单记录", "user_id", userID, "module", module, "usage", usage, "cost", cost, "calls", calls)
	}

	return nil
}

// RegisterCronJobs 注册定时任务
func RegisterCronJobs() {
	logger.Info("Registering billing system cron jobs...")

	// 获取定时任务服务单例
	cronService := GetCronService()
	if cronService == nil {
		logger.Error("获取定时任务服务实例失败，跳过定时任务注册")
		return
	}

	// 注册定时任务

	// 每5秒检查一次欠费的key（开发测试用，生产环境建议改为5分钟）
	_, err := cronService.scheduler.NewJob(
		gocron.DurationJob(time.Minute*5),
		gocron.NewTask(func() {
			cronService.CheckOverdueKeys()
		}),
	)
	if err != nil {
		logger.Error("注册欠费key检查定时任务失败", "error", err)
	} else {
		logger.Info("欠费key检查定时任务注册成功 - 每5分钟执行")
	}

	// 每10分钟检查一次过期的应用
	_, err = cronService.scheduler.NewJob(
		gocron.DurationJob(time.Minute*10),
		gocron.NewTask(func() {
			cronService.CheckExpiredApps()
		}),
	)
	if err != nil {
		logger.Error("注册过期应用检查定时任务失败", "error", err)
	} else {
		logger.Info("过期应用检查定时任务注册成功 - 每10分钟执行")
	}

	// 每小时生成账单统计数据（在每小时的第5分钟执行）
	_, err = cronService.scheduler.NewJob(
		gocron.CronJob("5,55 * * * *", false), // 每小时的第5分钟执行和每小时的第55分钟执行
		// gocron.DurationJob(time.Second*5),
		gocron.NewTask(func() {
			cronService.GenerateHourlyBillingStats()
		}),
	)
	if err != nil {
		logger.Error("注册每小时账单统计定时任务失败", "error", err)
	} else {
		logger.Info("每小时账单统计定时任务注册成功 - 每小时第5分钟执行")
	}

	// 启动调度器
	cronService.scheduler.Start()
	logger.Info("定时任务调度器已启动")
}

// StopCronJobs 停止定时任务
func StopCronJobs() {
	cronService := GetCronService()
	if cronService != nil && cronService.scheduler != nil {
		if err := cronService.scheduler.Shutdown(); err != nil {
			logger.Error("停止定时任务调度器失败", "error", err)
		} else {
			logger.Info("定时任务调度器已停止")
		}
	}
}
