package billing

import (
	"testing"
	"time"

	"git.uozi.org/uozi/potato-billing-api/model"
	"git.uozi.org/uozi/potato-billing-api/query"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
)

func TestGenerateHourlyBillingStats(t *testing.T) {
	// 这是一个示例测试，展示如何测试账单统计功能
	// 在实际环境中，你需要设置测试数据库

	t.Skip("需要数据库连接才能运行此测试")

	// 初始化数据库连接
	// db := setupTestDB()
	// cosy.SetDB(db)

	// 创建测试数据
	now := time.Now()
	lastHour := now.Add(-time.Hour)
	
	// 创建测试用户
	testUser := &model.User{
		Name:    "test_user",
		Email:   "<EMAIL>",
		Balance: 100.0,
	}
	
	// 创建测试应用
	testApp := &model.App{
		Name:   "test_app",
		APIKey: "test_api_key",
		UserID: testUser.ID,
	}

	// 创建测试使用日志
	testUsageLog := &model.UsageLog{
		AppID:    testApp.ID,
		UserID:   testUser.ID,
		Module:   "llm",
		Usage:    1000,
		Cost:     0.5,
		Currency: "CNY",
		Unit:     "token",
	}
	testUsageLog.CreatedAt = lastHour.UnixMilli()

	// 保存测试数据
	err := query.User.Create(testUser)
	assert.NoError(t, err)

	err = query.App.Create(testApp)
	assert.NoError(t, err)

	err = query.UsageLog.Create(testUsageLog)
	assert.NoError(t, err)

	// 创建CronService实例
	cronService := GetCronService()
	assert.NotNil(t, cronService)

	// 执行账单统计
	cronService.GenerateHourlyBillingStats()

	// 验证账单记录是否创建
	billingRecords, err := query.Billing.
		Where(query.Billing.UserID.Eq(testUser.ID)).
		Where(query.Billing.Module.Eq("llm")).
		Find()
	
	assert.NoError(t, err)
	assert.Len(t, billingRecords, 1)

	billing := billingRecords[0]
	assert.Equal(t, testUser.ID, billing.UserID)
	assert.Equal(t, "llm", billing.Module)
	assert.Equal(t, int64(1000), billing.Usage)
	assert.Equal(t, decimal.NewFromFloat(0.5), billing.Cost)
	assert.Equal(t, int64(1), billing.Calls)

	// 清理测试数据
	query.Billing.Where(query.Billing.UserID.Eq(testUser.ID)).Delete()
	query.UsageLog.Where(query.UsageLog.UserID.Eq(testUser.ID)).Delete()
	query.App.Where(query.App.UserID.Eq(testUser.ID)).Delete()
	query.User.Where(query.User.ID.Eq(testUser.ID)).Delete()
}

func TestAggregateUsageLogsToBilling(t *testing.T) {
	t.Skip("需要数据库连接才能运行此测试")
	
	// 这里可以添加更详细的聚合逻辑测试
	// 测试不同时间范围、多个用户、多个模块的聚合
}

func TestCreateOrUpdateBillingRecord(t *testing.T) {
	t.Skip("需要数据库连接才能运行此测试")
	
	// 这里可以测试创建和更新账单记录的逻辑
	// 测试重复时间段的处理
}