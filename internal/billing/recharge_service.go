package billing

import (
	"context"
	"fmt"
	"time"

	"git.uozi.org/uozi/potato-billing-api/model"
	"git.uozi.org/uozi/potato-billing-api/query"
	"git.uozi.org/uozi/potato-billing-api/types"
	"github.com/uozi-tech/cosy"
	"github.com/uozi-tech/cosy/logger"
	"gorm.io/gorm"
)

// CreateRechargeRequest 创建充值记录请求
type CreateRechargeRequest struct {
	UserID      uint64  `json:"user_id,string" binding:"required"`
	Amount      float64 `json:"amount" binding:"required,min=0.01"`
	Type        string  `json:"type,omitempty"`
	Description string  `json:"description,omitempty"`
}

// RechargeStatsResponse 充值统计响应
type RechargeStatsResponse struct {
	TotalAmount    float64 `json:"total_amount"`
	TotalCount     int64   `json:"total_count"`
	TodayAmount    float64 `json:"today_amount"`
	TodayCount     int64   `json:"today_count"`
	MonthAmount    float64 `json:"month_amount"`
	MonthCount     int64   `json:"month_count"`
	PendingAmount  float64 `json:"pending_amount"`
	PendingCount   int64   `json:"pending_count"`
	CompletedCount int64   `json:"completed_count"`
	CancelledCount int64   `json:"cancelled_count"`
}

// UserBalanceInfo 用户余额信息
type UserBalanceInfo struct {
	ID           string  `json:"id"`
	Name         string  `json:"name"`
	Email        string  `json:"email"`
	Balance      float64 `json:"balance"`
	LastRecharge string  `json:"last_recharge"`
}

// RechargeService 充值服务
type RechargeService struct {
	appService *AppService
}

// NewRechargeService 创建充值服务
func NewRechargeService(appService *AppService) *RechargeService {
	return &RechargeService{
		appService: appService,
	}
}

// CreateRecharge 创建充值记录（管理员充值）
func (rs *RechargeService) CreateRecharge(ctx context.Context, req *CreateRechargeRequest, operatorID uint64) error {
	// 检查用户是否存在
	user, err := query.User.Where(query.User.ID.Eq(req.UserID)).First()
	if err != nil {
		return fmt.Errorf("用户不存在: %w", err)
	}

	// 使用事务处理充值
	db := cosy.UseDB(ctx)
	err = db.Transaction(func(tx *gorm.DB) error {
		// 创建充值记录
		rechargeRecord := &model.RechargeRecord{
			UserID:      req.UserID,
			Amount:      req.Amount,
			Type:        req.Type,
			Status:      types.RechargeStatusCompleted, // 管理员充值直接完成
			Description: req.Description,
			OperatorID:  operatorID,
		}

		if err := tx.Create(rechargeRecord).Error; err != nil {
			return fmt.Errorf("创建充值记录失败: %w", err)
		}

		// 增加用户余额
		err := tx.Model(user).
			Update("balance", gorm.Expr("balance + ?", req.Amount)).
			Error
		if err != nil {
			return fmt.Errorf("更新用户余额失败: %w", err)
		}

		return nil
	})

	if err != nil {
		return err
	}

	// 充值成功后，检查并恢复被阻塞的应用状态
	if rs.appService != nil {
		err := rs.appService.CheckAndRestoreAppStatus(ctx, req.UserID, 0, "")
		if err != nil {
			logger.Error("充值后恢复应用状态失败", "error", err, "userID", req.UserID)
			// 不影响充值成功，只记录错误
		} else {
			logger.Info("充值后成功恢复应用状态", "userID", req.UserID)
		}
	}

	return nil
}

// ConfirmRecharge 确认充值（用于处理第三方支付回调）
func (rs *RechargeService) ConfirmRecharge(ctx context.Context, id uint64) error {
	// 获取充值记录
	rechargeRecord, err := query.RechargeRecord.Where(query.RechargeRecord.ID.Eq(id)).First()
	if err != nil {
		return fmt.Errorf("充值记录不存在: %w", err)
	}

	if rechargeRecord.Status != types.RechargeStatusPending {
		return fmt.Errorf("充值记录状态不正确，当前状态: %s", rechargeRecord.Status)
	}

	// 获取用户信息
	user, err := query.User.Where(query.User.ID.Eq(rechargeRecord.UserID)).First()
	if err != nil {
		return fmt.Errorf("用户不存在: %w", err)
	}

	// 使用事务处理确认
	db := cosy.UseDB(ctx)
	err = db.Transaction(func(tx *gorm.DB) error {
		// 更新充值记录状态
		err := tx.Model(rechargeRecord).Update("status", types.RechargeStatusCompleted).Error
		if err != nil {
			return fmt.Errorf("更新充值记录状态失败: %w", err)
		}

		// 增加用户余额
		err = tx.Model(user).
			Update("balance", gorm.Expr("balance + ?", rechargeRecord.Amount)).
			Error
		if err != nil {
			return fmt.Errorf("更新用户余额失败: %w", err)
		}

		return nil
	})

	if err != nil {
		return err
	}

	// 充值确认后，检查并恢复被阻塞的应用状态
	if rs.appService != nil {
		err := rs.appService.CheckAndRestoreAppStatus(ctx, rechargeRecord.UserID, 0, "")
		if err != nil {
			logger.Error("确认充值后恢复应用状态失败", "error", err, "userID", rechargeRecord.UserID)
		} else {
			logger.Info("确认充值后成功恢复应用状态", "userID", rechargeRecord.UserID)
		}
	}

	return nil
}

// CancelRecharge 取消充值
func (rs *RechargeService) CancelRecharge(ctx context.Context, id uint64) error {
	// 获取充值记录
	rechargeRecord, err := query.RechargeRecord.Where(query.RechargeRecord.ID.Eq(id)).First()
	if err != nil {
		return fmt.Errorf("充值记录不存在: %w", err)
	}

	if rechargeRecord.Status != types.RechargeStatusPending {
		return fmt.Errorf("只能取消待处理的充值记录，当前状态: %s", rechargeRecord.Status)
	}

	// 更新状态为已取消
	_, err = query.RechargeRecord.Where(query.RechargeRecord.ID.Eq(id)).Update(query.RechargeRecord.Status, types.RechargeStatusCancelled)
	if err != nil {
		return fmt.Errorf("取消充值失败: %w", err)
	}

	return nil
}

// GetRechargeStats 获取充值统计
func (rs *RechargeService) GetRechargeStats(ctx context.Context) (*RechargeStatsResponse, error) {
	db := cosy.UseDB(ctx)
	
	now := time.Now()
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())

	stats := &RechargeStatsResponse{}

	// 总充值金额和数量
	err := db.Model(&model.RechargeRecord{}).
		Where("status = ?", types.RechargeStatusCompleted).
		Select("COALESCE(SUM(amount), 0) as total_amount, COUNT(*) as total_count").
		Scan(stats).Error
	if err != nil {
		return nil, fmt.Errorf("获取总充值统计失败: %w", err)
	}

	// 今日充值金额和数量
	err = db.Model(&model.RechargeRecord{}).
		Where("status = ? AND created_at >= ?", types.RechargeStatusCompleted, todayStart.Unix()).
		Select("COALESCE(SUM(amount), 0) as today_amount, COUNT(*) as today_count").
		Scan(stats).Error
	if err != nil {
		return nil, fmt.Errorf("获取今日充值统计失败: %w", err)
	}

	// 本月充值金额和数量
	err = db.Model(&model.RechargeRecord{}).
		Where("status = ? AND created_at >= ?", types.RechargeStatusCompleted, monthStart.Unix()).
		Select("COALESCE(SUM(amount), 0) as month_amount, COUNT(*) as month_count").
		Scan(stats).Error
	if err != nil {
		return nil, fmt.Errorf("获取本月充值统计失败: %w", err)
	}

	// 待处理充值金额和数量
	err = db.Model(&model.RechargeRecord{}).
		Where("status = ?", types.RechargeStatusPending).
		Select("COALESCE(SUM(amount), 0) as pending_amount, COUNT(*) as pending_count").
		Scan(stats).Error
	if err != nil {
		return nil, fmt.Errorf("获取待处理充值统计失败: %w", err)
	}

	// 各状态数量统计
	var statusCounts []struct {
		Status string `json:"status"`
		Count  int64  `json:"count"`
	}
	err = db.Model(&model.RechargeRecord{}).
		Select("status, COUNT(*) as count").
		Group("status").
		Scan(&statusCounts).Error
	if err != nil {
		return nil, fmt.Errorf("获取状态统计失败: %w", err)
	}

	for _, sc := range statusCounts {
		switch sc.Status {
		case types.RechargeStatusCompleted:
			stats.CompletedCount = sc.Count
		case types.RechargeStatusCancelled:
			stats.CancelledCount = sc.Count
		}
	}

	return stats, nil
}

// GetUserBalances 获取用户余额信息
func (rs *RechargeService) GetUserBalances(ctx context.Context, page, pageSize int) ([]UserBalanceInfo, int64, error) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	db := cosy.UseDB(ctx)
	
	// 获取总数
	var total int64
	err := db.Model(&model.User{}).Count(&total).Error
	if err != nil {
		return nil, 0, fmt.Errorf("获取用户总数失败: %w", err)
	}

	// 分页查询用户余额信息
	var users []model.User
	offset := (page - 1) * pageSize
	err = db.Model(&model.User{}).
		Select("id, name, email, balance").
		Order("balance DESC").
		Limit(pageSize).
		Offset(offset).
		Find(&users).Error
	if err != nil {
		return nil, 0, fmt.Errorf("获取用户余额信息失败: %w", err)
	}

	// 转换为响应格式
	balances := make([]UserBalanceInfo, len(users))
	for i, user := range users {
		// 获取最后充值时间
		var lastRecharge time.Time
		db.Model(&model.RechargeRecord{}).
			Where("user_id = ? AND status = ?", user.ID, types.RechargeStatusCompleted).
			Order("created_at DESC").
			Limit(1).
			Pluck("created_at", &lastRecharge)

		balances[i] = UserBalanceInfo{
			ID:           fmt.Sprintf("%d", user.ID),
			Name:         user.Name,
			Email:        user.Email,
			Balance:      user.Balance,
			LastRecharge: lastRecharge.Format("2006-01-02 15:04:05"),
		}
	}

	return balances, total, nil
}
