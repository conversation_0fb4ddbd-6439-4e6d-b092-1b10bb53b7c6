package billing

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"git.uozi.org/uozi/potato-billing-api/model"
	"git.uozi.org/uozi/potato-billing-api/query"
	"github.com/uozi-tech/cosy"
)

// UsageHistoryQuery 用量历史查询参数
type UsageHistoryQuery struct {
	AppID     uint64 `form:"app_id,string" binding:"required"`
	Module    string `form:"module"`
	StartTime int64  `form:"start_time"`
	EndTime   int64  `form:"end_time"`
	Page      int    `form:"page"`
	PageSize  int    `form:"pageSize"`
}

// UsageHistoryResponse 用量历史响应结构
type UsageHistoryResponse struct {
	Data       []*model.UsageLog      `json:"data"`
	Pagination map[string]interface{} `json:"pagination"`
}

// UsageStatsResponse 用量统计响应结构
type UsageStatsResponse struct {
	AppID         string             `json:"app_id"`
	Period        string             `json:"period"`
	TotalUsage    int64              `json:"total_usage"`
	TotalCost     float64            `json:"total_cost"`
	TotalRequests int64              `json:"total_requests"`
	ModuleStats   []ModuleUsageStats `json:"module_stats"`
	DailyStats    []DailyUsageStats  `json:"daily_stats"`
}

// ModuleUsageStats 模块用量统计
type ModuleUsageStats struct {
	Module   string  `json:"module"`
	Usage    int64   `json:"usage"`
	Cost     float64 `json:"cost"`
	Requests int64   `json:"requests"`
}

// DailyUsageStats 每日用量统计
type DailyUsageStats struct {
	Date     string  `json:"date"`
	Usage    int64   `json:"usage"`
	Cost     float64 `json:"cost"`
	Requests int64   `json:"requests"`
}

// UsageService 用量统计服务
type UsageService struct{}

// NewUsageService 创建用量统计服务
func NewUsageService() *UsageService {
	return &UsageService{}
}

// GetAppUsageHistory 获取应用用量历史
func (us *UsageService) GetAppUsageHistory(ctx context.Context, queryParams *UsageHistoryQuery) (*UsageHistoryResponse, error) {
	q := query.UsageLog

	queryBuilder := q.Select(q.ALL).Where(q.AppID.Eq(queryParams.AppID))

	// 添加模块过滤
	if queryParams.Module != "" {
		queryBuilder = queryBuilder.Where(q.Module.Eq(queryParams.Module))
	}

	// 添加时间范围过滤
	if queryParams.StartTime > 0 {
		queryBuilder = queryBuilder.Where(q.CreatedAt.Gte(queryParams.StartTime))
	}
	if queryParams.EndTime > 0 {
		queryBuilder = queryBuilder.Where(q.CreatedAt.Lte(queryParams.EndTime))
	}

	// 获取总数
	total, err := queryBuilder.Count()
	if err != nil {
		return nil, fmt.Errorf("获取用量历史总数失败: %w", err)
	}

	// 设置分页参数
	if queryParams.Page < 1 {
		queryParams.Page = 1
	}
	if queryParams.PageSize < 1 || queryParams.PageSize > 100 {
		queryParams.PageSize = 20
	}

	// 分页查询
	offset := (queryParams.Page - 1) * queryParams.PageSize
	usageLogs, err := queryBuilder.
		Preload(q.App).
		Order(q.CreatedAt.Desc()).
		Limit(queryParams.PageSize).
		Offset(offset).
		Find()
	if err != nil {
		return nil, fmt.Errorf("获取用量历史失败: %w", err)
	}

	// 构建响应
	response := &UsageHistoryResponse{
		Data: usageLogs,
		Pagination: map[string]interface{}{
			"total":        total,
			"per_page":     queryParams.PageSize,
			"current_page": queryParams.Page,
			"total_pages":  (total + int64(queryParams.PageSize) - 1) / int64(queryParams.PageSize),
		},
	}

	return response, nil
}

// GetAppUsageStats 获取应用用量统计
func (us *UsageService) GetAppUsageStats(ctx context.Context, appID, period string) (*UsageStatsResponse, error) {
	db := cosy.UseDB(ctx)

	// 解析应用ID
	appIDUint, err := strconv.ParseUint(appID, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("无效的应用ID: %w", err)
	}

	// 计算时间范围
	now := time.Now()
	var startTime time.Time
	switch period {
	case "day":
		startTime = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	case "week":
		startTime = now.AddDate(0, 0, -7)
	case "month":
		startTime = now.AddDate(0, -1, 0)
	case "year":
		startTime = now.AddDate(-1, 0, 0)
	default:
		startTime = now.AddDate(0, -1, 0) // 默认一个月
	}

	// 基础查询条件
	baseQuery := db.Model(&model.UsageLog{}).
		Where("app_id = ? AND created_at >= ?", appIDUint, startTime.Unix())

	// 获取总体统计
	var totalStats struct {
		TotalUsage    int64   `json:"total_usage"`
		TotalCost     float64 `json:"total_cost"`
		TotalRequests int64   `json:"total_requests"`
	}

	err = baseQuery.
		Select("COALESCE(SUM(usage), 0) as total_usage, COALESCE(SUM(cost), 0) as total_cost, COUNT(*) as total_requests").
		Scan(&totalStats).Error
	if err != nil {
		return nil, fmt.Errorf("获取总体统计失败: %w", err)
	}

	// 获取按模块统计
	var moduleStats []ModuleUsageStats
	err = baseQuery.
		Select("module, COALESCE(SUM(usage), 0) as usage, COALESCE(SUM(cost), 0) as cost, COUNT(*) as requests").
		Group("module").
		Scan(&moduleStats).Error
	if err != nil {
		return nil, fmt.Errorf("获取模块统计失败: %w", err)
	}

	// 获取每日统计（最近30天）
	var dailyStats []DailyUsageStats
	err = baseQuery.
		Select("DATE(FROM_UNIXTIME(created_at)) as date, COALESCE(SUM(usage), 0) as usage, COALESCE(SUM(cost), 0) as cost, COUNT(*) as requests").
		Group("DATE(FROM_UNIXTIME(created_at))").
		Order("date DESC").
		Limit(30).
		Scan(&dailyStats).Error
	if err != nil {
		return nil, fmt.Errorf("获取每日统计失败: %w", err)
	}

	// 构建响应
	response := &UsageStatsResponse{
		AppID:         appID,
		Period:        period,
		TotalUsage:    totalStats.TotalUsage,
		TotalCost:     totalStats.TotalCost,
		TotalRequests: totalStats.TotalRequests,
		ModuleStats:   moduleStats,
		DailyStats:    dailyStats,
	}

	return response, nil
}

// GetUserUsageStats 获取用户用量统计
func (us *UsageService) GetUserUsageStats(ctx context.Context, userID uint64, period string) (*UsageStatsResponse, error) {
	db := cosy.UseDB(ctx)

	// 计算时间范围
	now := time.Now()
	var startTime time.Time
	switch period {
	case "day":
		startTime = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	case "week":
		startTime = now.AddDate(0, 0, -7)
	case "month":
		startTime = now.AddDate(0, -1, 0)
	case "year":
		startTime = now.AddDate(-1, 0, 0)
	default:
		startTime = now.AddDate(0, -1, 0) // 默认一个月
	}

	// 基础查询条件
	baseQuery := db.Model(&model.UsageLog{}).
		Where("user_id = ? AND created_at >= ?", userID, startTime.Unix())

	// 获取总体统计
	var totalStats struct {
		TotalUsage    int64   `json:"total_usage"`
		TotalCost     float64 `json:"total_cost"`
		TotalRequests int64   `json:"total_requests"`
	}

	err := baseQuery.
		Select("COALESCE(SUM(usage), 0) as total_usage, COALESCE(SUM(cost), 0) as total_cost, COUNT(*) as total_requests").
		Scan(&totalStats).Error
	if err != nil {
		return nil, fmt.Errorf("获取用户总体统计失败: %w", err)
	}

	// 获取按模块统计
	var moduleStats []ModuleUsageStats
	err = baseQuery.
		Select("module, COALESCE(SUM(usage), 0) as usage, COALESCE(SUM(cost), 0) as cost, COUNT(*) as requests").
		Group("module").
		Scan(&moduleStats).Error
	if err != nil {
		return nil, fmt.Errorf("获取用户模块统计失败: %w", err)
	}

	// 获取每日统计
	var dailyStats []DailyUsageStats
	err = baseQuery.
		Select("DATE(FROM_UNIXTIME(created_at)) as date, COALESCE(SUM(usage), 0) as usage, COALESCE(SUM(cost), 0) as cost, COUNT(*) as requests").
		Group("DATE(FROM_UNIXTIME(created_at))").
		Order("date DESC").
		Limit(30).
		Scan(&dailyStats).Error
	if err != nil {
		return nil, fmt.Errorf("获取用户每日统计失败: %w", err)
	}

	// 构建响应
	response := &UsageStatsResponse{
		AppID:         fmt.Sprintf("user_%d", userID),
		Period:        period,
		TotalUsage:    totalStats.TotalUsage,
		TotalCost:     totalStats.TotalCost,
		TotalRequests: totalStats.TotalRequests,
		ModuleStats:   moduleStats,
		DailyStats:    dailyStats,
	}

	return response, nil
}

// GetSystemUsageOverview 获取系统用量概览
func (us *UsageService) GetSystemUsageOverview(ctx context.Context) (map[string]interface{}, error) {
	db := cosy.UseDB(ctx)

	now := time.Now()
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())

	overview := make(map[string]interface{})

	// 今日统计
	var todayStats struct {
		TotalUsage    int64   `json:"total_usage"`
		TotalCost     float64 `json:"total_cost"`
		TotalRequests int64   `json:"total_requests"`
	}
	err := db.Model(&model.UsageLog{}).
		Where("created_at >= ?", todayStart.Unix()).
		Select("COALESCE(SUM(usage), 0) as total_usage, COALESCE(SUM(cost), 0) as total_cost, COUNT(*) as total_requests").
		Scan(&todayStats).Error
	if err != nil {
		return nil, fmt.Errorf("获取今日统计失败: %w", err)
	}
	overview["today"] = todayStats

	// 本月统计
	var monthStats struct {
		TotalUsage    int64   `json:"total_usage"`
		TotalCost     float64 `json:"total_cost"`
		TotalRequests int64   `json:"total_requests"`
	}
	err = db.Model(&model.UsageLog{}).
		Where("created_at >= ?", monthStart.Unix()).
		Select("COALESCE(SUM(usage), 0) as total_usage, COALESCE(SUM(cost), 0) as total_cost, COUNT(*) as total_requests").
		Scan(&monthStats).Error
	if err != nil {
		return nil, fmt.Errorf("获取本月统计失败: %w", err)
	}
	overview["month"] = monthStats

	// 按模块统计（本月）
	var moduleStats []ModuleUsageStats
	err = db.Model(&model.UsageLog{}).
		Where("created_at >= ?", monthStart.Unix()).
		Select("module, COALESCE(SUM(usage), 0) as usage, COALESCE(SUM(cost), 0) as cost, COUNT(*) as requests").
		Group("module").
		Scan(&moduleStats).Error
	if err != nil {
		return nil, fmt.Errorf("获取模块统计失败: %w", err)
	}
	overview["modules"] = moduleStats

	return overview, nil
}
