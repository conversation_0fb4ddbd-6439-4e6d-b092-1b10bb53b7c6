package user

import (
	"git.uozi.org/uozi/potato-billing-api/model"
	"git.uozi.org/uozi/potato-billing-api/query"
	"github.com/uozi-tech/cosy/logger"
	"golang.org/x/crypto/bcrypt"
)

const (
	SourceClient = "client"
	SourceAdmin  = "admin"
)

func Login(email string, password string, source string) (user *model.User, err error) {
	u := query.User

	user, err = u.Where(u.Email.Eq(email)).First()
	if err != nil {
		logger.Error(err)
		return nil, ErrPasswordIncorrect
	}

	if user.UserType == model.UserTypeClient && source != SourceClient {
		return nil, ErrUserNotAllowed
	}

	if user.Status == model.UserStatusBlocked {
		return nil, ErrUserBlocked
	}

	if err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(password)); err != nil {
		logger.Error(err)
		return nil, ErrPasswordIncorrect
	}

	return
}
