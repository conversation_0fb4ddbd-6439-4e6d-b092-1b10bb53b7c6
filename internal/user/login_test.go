package user

import (
	"context"
	"testing"

	"git.uozi.org/uozi/potato-billing-api/model"
	"git.uozi.org/uozi/potato-billing-api/query"
	"github.com/stretchr/testify/assert"
	"github.com/uozi-tech/cosy"
	"github.com/uozi-tech/cosy/sandbox"
	"golang.org/x/crypto/bcrypt"
)

func TestLogin(t *testing.T) {
	sandbox.NewInstance("../../app.testing.ini", "mysql").
		RegisterModels(model.User{}).
		Run(func(instance *sandbox.Instance) {
			db := cosy.UseDB(context.Background())
			query.Init(db)
			model.Use(db)

			pwd, _ := bcrypt.GenerateFromPassword([]byte("test"), bcrypt.DefaultCost)

			db.Create(&[]model.User{{
				Email:    "test",
				Password: string(pwd),
				Status:   model.UserStatusActive,
				UserType: model.UserTypeAdmin,
			}, {
				Email:    "banned",
				Password: string(pwd),
				Status:   model.UserStatusBlocked,
				UserType: model.UserTypeAdmin,
			}})

			t.Run("success", func(t *testing.T) {
				user, err := Login("test", "test", SourceAdmin)
				if err != nil {
					t.Error(err)
					return
				}
				assert.NotNil(t, user)
			})

			t.Run("password incorrect", func(t *testing.T) {
				_, err := Login("test", "123456", SourceAdmin)
				assert.Equal(t, ErrPasswordIncorrect, err)
				_, err = Login("123456", "123456", SourceAdmin)
				assert.Equal(t, ErrPasswordIncorrect, err)
			})

			t.Run("user banned", func(t *testing.T) {
				_, err := Login("banned", "test", SourceAdmin)
				assert.Equal(t, ErrUserBlocked, err)
			})
		})
}
