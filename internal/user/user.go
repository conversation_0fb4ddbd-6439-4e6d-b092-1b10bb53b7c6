package user

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"path/filepath"
	"time"

	"git.uozi.org/uozi/potato-billing-api/internal/helper"
	"git.uozi.org/uozi/potato-billing-api/internal/oss"
	"git.uozi.org/uozi/potato-billing-api/model"
	"git.uozi.org/uozi/potato-billing-api/query"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/uozi-tech/cosy"
	"github.com/uozi-tech/cosy/logger"
	"github.com/uozi-tech/cosy/map2struct"
	"github.com/uozi-tech/cosy/redis"
	"gorm.io/gorm"
)

// GetByID get user by id (with redis cache)
func GetByID(id uint64) (user *model.User, err error) {
	key := helper.BuildUserKey(id)
	userStr, err := redis.Get(key)
	if err != nil || userStr == "" {
		u := query.User
		user, err = u.Preload(u.Avatar).FirstByID(id)

		if err != nil {
			return
		}
		// hide password
		user.Password = ""
		bytes, _ := json.Marshal(user)
		_ = redis.Set(key, string(bytes), 5*time.Minute)

		return
	}

	user = &model.User{}
	err = json.Unmarshal([]byte(userStr), user)

	if err != nil {
		return nil, err
	}

	return
}

func CurrentToken(c *gin.Context) (token string) {
	if len(c.Request.Header["Token"]) == 0 {
		if c.Query("token") == "" {
			return ""
		}
		tmp, _ := base64.StdEncoding.DecodeString(c.Query("token"))
		token = string(tmp)
	} else {
		token = c.Request.Header["Token"][0]
	}
	return token
}

// CurrentUser get current user from token
func CurrentUser(c *gin.Context) (u *model.User, err error) {
	// validate token
	token := CurrentToken(c)

	if token == "" {
		return nil, errors.New("token not found")
	}

	var claims *JWTClaims
	claims, err = ValidateJWT(token)
	if err != nil {
		return
	}

	// get user by id
	u, err = GetByID(claims.UserID)
	if err != nil {
		return
	}

	_, err = redis.Get("token:" + token)
	if err != nil {
		logger.Error("token not found in redis", token)
		return nil, err
	}
	expiresAt := redis.TTL("token:" + token).Seconds()

	if expiresAt <= 0 {
		logger.Error("token expired", token)
		return nil, errors.New("token expired")
	}

	if expiresAt < 30*60 {
		err = redis.Set("token:"+token, u.ID, 1*time.Minute)
		if err != nil {
			return nil, err
		}
		// refresh token
		newToken, err := GenerateJWT(u.ID)
		if err != nil {
			return nil, err
		}
		err = redis.Set("token:"+newToken, u.ID, 3*time.Hour)
		if err != nil {
			return nil, err
		}
		c.Header("refresh-token", newToken)
	}

	u.UpdateLastActive()

	logger.Info("[Current User]", u.Name)

	return
}

// GetCurrentUserWithAvatar 获取当前用户信息（包含头像）
func GetCurrentUserWithAvatar(userID uint64) (*model.User, error) {
	user, err := GetByID(userID)
	if err != nil {
		return nil, err
	}

	// 如果用户有头像，加载头像信息
	if user.AvatarID != 0 {
		u := query.Upload
		avatar, err := u.Where(u.ID.Eq(user.AvatarID)).First()
		if err != nil {
			logger.Error("Failed to load user avatar", "error", err, "user_id", userID, "avatar_id", user.AvatarID)
			// 不因为头像加载失败而返回错误，继续返回用户信息
		} else {
			user.Avatar = avatar
		}
	}

	return user, nil
}

// UpdateUserRequest 更新用户请求结构
type UpdateUserRequest struct {
	Name   string      `json:"name,omitempty"`
	Phone  string      `json:"phone,omitempty"`
	Email  string      `json:"email,omitempty"`
	Avatar interface{} `json:"avatar,omitempty"`
}

// UpdateCurrentUser 更新当前用户信息
func UpdateCurrentUser(ctx context.Context, userID uint64, req *UpdateUserRequest) (*model.User, error) {
	user, err := GetByID(userID)
	if err != nil {
		return nil, err
	}

	var upload *model.Upload
	var avatarID uint64

	// 处理头像上传
	if req.Avatar != nil {
		o, err := oss.NewOSS()
		if err != nil {
			return nil, fmt.Errorf("failed to initialize OSS: %w", err)
		}

		uploadRes := oss.UploadResult{}
		err = map2struct.WeakDecode(req.Avatar, &uploadRes)
		if err != nil {
			return nil, fmt.Errorf("failed to decode avatar upload result: %w", err)
		}

		dest := fmt.Sprintf("avatar/%v", uuid.New().String()+filepath.Ext(uploadRes.Filename))
		err = o.CopyObject(uploadRes.Url, dest)
		if err != nil {
			return nil, fmt.Errorf("failed to copy avatar to OSS: %w", err)
		}

		upload = &model.Upload{
			Name: uploadRes.Filename,
			Path: dest,
			Size: uploadRes.Size,
			MIME: uploadRes.MIME,
			To:   "avatar",
		}
	}

	// 使用事务更新用户信息
	db := cosy.UseDB(ctx)
	err = db.Transaction(func(tx *gorm.DB) error {
		if upload != nil {
			// 创建新头像记录
			err := tx.Create(upload).Error
			if err != nil {
				return fmt.Errorf("failed to create upload record: %w", err)
			}

			// 获取创建的头像记录
			err = tx.Model(&model.Upload{}).Where(
				query.Upload.Name.Eq(upload.Name),
				query.Upload.Path.Eq(upload.Path),
				query.Upload.To.Eq(upload.To),
			).First(upload).Error
			if err != nil {
				return fmt.Errorf("failed to get created upload record: %w", err)
			}

			// 将旧的头像标记为删除
			if user.AvatarID != 0 {
				err = tx.Model(&model.Upload{}).Where(query.Upload.ID.Eq(user.AvatarID)).Update(
					"deleted_at",
					time.Now().Unix(),
				).Error
				if err != nil {
					return fmt.Errorf("failed to mark old avatar as deleted: %w", err)
				}

				// OSS 删除失败不影响主流程
				if user.Avatar != nil && user.Avatar.Path != "" {
					o, _ := oss.NewOSS()
					if o != nil {
						_ = o.DeleteObject(user.Avatar.Path)
					}
				}
			}

			avatarID = upload.ID
		}

		// 更新用户信息
		updateData := &model.User{
			Name:  req.Name,
			Phone: req.Phone,
			Email: req.Email,
		}
		if upload != nil {
			updateData.AvatarID = avatarID
		}

		err = tx.Model(&model.User{Model: model.Model{ID: userID}}).
			Where(query.User.ID.Eq(userID)).
			Updates(updateData).Error
		if err != nil {
			return fmt.Errorf("failed to update user: %w", err)
		}

		// 删除缓存
		redis.Del(helper.BuildUserKey(userID))

		return nil
	})

	if err != nil {
		return nil, err
	}

	// 返回更新后的用户信息
	u := query.User
	updatedUser, err := u.Where(u.ID.Eq(userID)).Preload(u.Avatar).First()
	if err != nil {
		return nil, fmt.Errorf("failed to get updated user: %w", err)
	}

	// 隐藏密码
	updatedUser.Password = ""

	return updatedUser, nil
}

// UpdateUserAvatar 更新用户头像
func UpdateUserAvatar(userID, avatarID uint64) (*model.User, error) {
	// 更新头像
	u := query.User
	_, err := u.Where(u.ID.Eq(userID)).Update(u.AvatarID, avatarID)
	if err != nil {
		return nil, fmt.Errorf("failed to update user avatar: %w", err)
	}

	// 获取更新后的用户信息
	user, err := u.Preload(u.Avatar).Where(u.ID.Eq(userID)).First()
	if err != nil {
		return nil, fmt.Errorf("failed to get updated user: %w", err)
	}

	// 隐藏密码
	user.Password = ""

	// 删除缓存
	redis.Del(helper.BuildUserKey(userID))

	return user, nil
}
