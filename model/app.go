package model

import (
	"github.com/google/uuid"
	"github.com/uozi-tech/cosy/sonyflake"
	"gorm.io/gorm"
)

// App 应用表
type App struct {
	Model
	APIKey  string `json:"api_key" gorm:"not null;type:varbinary(5120);serializer:crypto"`                                    // API Key
	Name    string `json:"name" gorm:"type:varchar(255)" cosy:"add:required;update:omitempty;list:fussy"`                     // 应用名称
	Status  string `json:"status" gorm:"not null;default:'ok';type:varchar(20)" cosy:"add:required;update:omitempty;list:in"` // 'ok' | 'blocked'
	UserID  uint64 `json:"user_id,string" gorm:"index" cosy:"add:required;list:fussy"`                                        // 关联用户ID
	User    *User  `json:"user,omitempty" gorm:"foreignKey:UserID" cosy:"list:preload"`                                       // 关联用户
	Comment string `json:"comment" gorm:"type:text" cosy:"all:omitempty"`
}

// BeforeCreate 创建前生成ID
func (a *App) BeforeCreate(_ *gorm.DB) error {
	if a.ID == 0 {
		a.ID = sonyflake.NextID()
	}
	return nil
}

// GenerateAPIKey 生成API Key
func (a *App) GenerateAPIKey() string {
	a.APIKey = "uz-" + uuid.New().String()
	return a.APIKey
}
