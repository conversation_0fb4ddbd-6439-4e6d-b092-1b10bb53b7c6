package model

import (
	"github.com/shopspring/decimal"
	"github.com/uozi-tech/cosy/sonyflake"
)

// Billing 账单记录（每小时）
type Billing struct {
	Model
	AppID  uint64          `gorm:"index"`
	App    *App            `gorm:"foreignKey:AppID"`
	UserID uint64          `gorm:"index"`
	User   *User           `gorm:"foreignKey:UserID"`
	Module string          `gorm:"index"`
	Cost   decimal.Decimal `gorm:"type:decimal(10,6);"`
	Usage  int64
	Calls  int64
}

func (b *Billing) BeforeCreate() error {
	if b.ID == 0 {
		b.ID = sonyflake.NextID()
	}
	return nil
}
