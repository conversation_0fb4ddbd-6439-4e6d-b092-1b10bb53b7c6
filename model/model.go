package model

import (
	"git.uozi.org/uozi/potato-billing-api/internal/helper"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy/logger"
	"github.com/uozi-tech/cosy/redis"
	"gorm.io/gen"
	"gorm.io/gorm"
	"gorm.io/plugin/soft_delete"
)

var db *gorm.DB

type Model struct {
	ID        uint64                `gorm:"primary_key" json:"id,string"`
	CreatedAt int64                 `json:"created_at,omitempty" gorm:"autoCreateTime:milli"`
	UpdatedAt int64                 `json:"updated_at,omitempty" gorm:"autoUpdateTime:milli"`
	DeletedAt soft_delete.DeletedAt `json:"deleted_at,omitempty" gorm:"index;default:0;softDelete:milli"`
}

func GenerateAllModel() []any {
	return []any{
		User{},
		Upload{},
		Setting{},
		App{},
		UsageLog{},
		PricingRule{},
		RechargeRecord{},
		QuotaPackageRecord{},
		Billing{},
	}
}

func Use(tx *gorm.DB) {
	db = tx
}

type Method interface {
	// FirstByID Where("id=@id")
	FirstByID(id uint64) (*gen.T, error)
	// DeleteByID update @@table set deleted_at=NOW() where id=@id
	DeleteByID(id uint64) error
}

func DropCache(prefix string, ids ...uint64) {
	keys := make([]string, 0)
	for _, id := range ids {
		if id != 0 {
			keys = append(keys, helper.BuildKey(prefix, cast.ToString(id)))
		}
	}
	err := redis.Del(keys...)
	if err != nil {
		logger.Debug(err)
	}
}
