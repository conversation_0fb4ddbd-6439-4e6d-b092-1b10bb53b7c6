package model

import (
	"fmt"
	"strings"

	"github.com/uozi-tech/cosy/sonyflake"
	"gorm.io/gorm"
)

// PricingRule 价格规则表
type PricingRule struct {
	Model
	Module      string  `json:"module" gorm:"not null;type:varchar(50)" cosy:"add:required;update:omitempty;list:in"`     // 'llm' | 'tts' | 'asr'
	ModelName   string  `json:"model_name" gorm:"type:varchar(100)" cosy:"all:omitempty;list:fussy"`                      // 模型名称，可为空表示通用规则
	UnitPrice   float64 `json:"unit_price" gorm:"type:decimal(10,6);not null" cosy:"add:required,min=0;update:omitempty"` // 单价
	Currency    string  `json:"currency" gorm:"default:'CNY';type:varchar(10)" cosy:"all:omitempty"`                      // 货币类型
	Unit        string  `json:"unit" gorm:"default:'token';type:varchar(20)" cosy:"all:omitempty"`                        // 计费单位
	BaseUnit    int64   `json:"base_unit" gorm:"default:1" cosy:"all:omitempty"`                                          // 基数单位，如1000000表示百万，1000表示千
	IsActive    bool    `json:"is_active" gorm:"default:true" cosy:"all:omitempty"`                                       // 是否激活
	Priority    int     `json:"priority" gorm:"default:0" cosy:"all:omitempty"`                                           // 优先级，数字越大优先级越高
	Description string  `json:"description" gorm:"type:text" cosy:"all:omitempty"`                                        // 规则描述
}

// BeforeCreate 创建前生成ID
func (pr *PricingRule) BeforeCreate(_ *gorm.DB) error {
	if pr.ID == 0 {
		pr.ID = sonyflake.NextID()
	}
	// 设置默认基数单位
	if pr.BaseUnit == 0 {
		pr.BaseUnit = 1
	}
	return nil
}

// CalculateCost 根据基数单位计算费用
func (pr *PricingRule) CalculateCost(usage int64) float64 {
	// 将使用量转换为基数单位
	baseUsage := float64(usage) / float64(pr.BaseUnit)
	return baseUsage * pr.UnitPrice
}

// GetEffectiveUnitPrice 获取实际单价（考虑基数单位）
func (pr *PricingRule) GetEffectiveUnitPrice() float64 {
	return pr.UnitPrice / float64(pr.BaseUnit)
}

// GetDisplayPrice 获取显示用的价格字符串
func (pr *PricingRule) GetDisplayPrice() string {
	symbol := "¥"
	if pr.Currency == "USD" {
		symbol = "$"
	}

	// 根据BaseUnit和Unit动态生成显示名称
	unitDisplay := pr.GetUnitDisplay()
	// 格式化价格，移除末尾的0，最多保留6位小数
	priceStr := strings.TrimRight(strings.TrimRight(fmt.Sprintf("%.6f", pr.UnitPrice), "0"), ".")
	return symbol + priceStr + "/" + unitDisplay
}

// GetUnitDisplay 获取单位显示名称
func (pr *PricingRule) GetUnitDisplay() string {
	if pr.BaseUnit <= 1 {
		return pr.Unit
	}

	var prefix string
	if pr.BaseUnit >= 1000000 {
		prefix = fmt.Sprintf("%.0f百万", float64(pr.BaseUnit)/1000000)
	} else if pr.BaseUnit >= 10000 {
		prefix = fmt.Sprintf("%.0f万", float64(pr.BaseUnit)/10000)
	} else if pr.BaseUnit >= 1000 {
		prefix = fmt.Sprintf("%.0f千", float64(pr.BaseUnit)/1000)
	} else {
		prefix = fmt.Sprintf("%d", pr.BaseUnit)
	}

	return prefix + pr.Unit
}
