/**
 * 业务相关常量定义
 */

import {
  APP_STATUS,
  BILLING_TYPE,
  BILLING_UNIT,
  QUOTA_STATUS,
  QUOTA_TYPE,
  RECHARGE_STATUS,
  RECHARGE_TYPE,
  SERVICE_MODULE,
  USER_STATUS,
  USER_TYPE,
} from './common'

// 模块名称映射
export const MODULE_NAMES = {
  [SERVICE_MODULE.LLM]: 'LLM',
  [SERVICE_MODULE.TTS]: 'TTS',
  [SERVICE_MODULE.ASR]: 'ASR',
} as const

// 用户状态文本映射
export const USER_STATUS_TEXT = {
  [USER_STATUS.ACTIVE]: '已激活',
  [USER_STATUS.BLOCKED]: '禁用',
} as const

// 用户类型文本映射
export const USER_TYPE_TEXT = {
  [USER_TYPE.ADMIN]: '管理员',
  [USER_TYPE.CLIENT]: '客户',
} as const

// App 状态文本映射
export const APP_STATUS_TEXT = {
  [APP_STATUS.OK]: '正常',
  [APP_STATUS.BLOCKED]: '阻止',
} as const

// 配额状态文本映射
export const QUOTA_STATUS_TEXT = {
  [QUOTA_STATUS.ACTIVE]: '活跃',
  [QUOTA_STATUS.EXPIRED]: '已过期',
  [QUOTA_STATUS.EXHAUSTED]: '已用完',
  [QUOTA_STATUS.DISABLED]: '已禁用',
} as const

// 充值状态文本映射
export const RECHARGE_STATUS_TEXT = {
  [RECHARGE_STATUS.COMPLETED]: '已完成',
  [RECHARGE_STATUS.PENDING]: '待处理',
  [RECHARGE_STATUS.FAILED]: '失败',
  [RECHARGE_STATUS.CANCELLED]: '已取消',
} as const

// 充值类型标签映射
export const RECHARGE_TYPE_LABELS = {
  [RECHARGE_TYPE.ADMIN]: '管理员充值',
  [RECHARGE_TYPE.ALIPAY]: '支付宝',
  [RECHARGE_TYPE.WECHAT]: '微信支付',
  [RECHARGE_TYPE.BANK]: '银行转账',
} as const

// 充值类型颜色映射
export const RECHARGE_TYPE_COLORS = {
  [RECHARGE_TYPE.ADMIN]: 'bg-purple-100 text-purple-800',
  [RECHARGE_TYPE.ALIPAY]: 'bg-blue-100 text-blue-800',
  [RECHARGE_TYPE.WECHAT]: 'bg-green-100 text-green-800',
  [RECHARGE_TYPE.BANK]: 'bg-orange-100 text-orange-800',
} as const

// 配额类型标签映射
export const QUOTA_TYPE_LABELS = {
  [QUOTA_TYPE.ADMIN]: '管理员赠送',
  [QUOTA_TYPE.PURCHASE]: '购买获得',
  [QUOTA_TYPE.PROMOTION]: '促销活动',
  [QUOTA_TYPE.GIFT]: '系统赠送',
} as const

// 模块颜色映射
export const MODULE_COLORS = {
  [SERVICE_MODULE.LLM]: {
    bg: 'bg-blue-500',
    text: 'text-blue-600',
    light: 'bg-blue-50',
  },
  [SERVICE_MODULE.TTS]: {
    bg: 'bg-green-500',
    text: 'text-green-600',
    light: 'bg-green-50',
  },
  [SERVICE_MODULE.ASR]: {
    bg: 'bg-purple-500',
    text: 'text-purple-600',
    light: 'bg-purple-50',
  },
} as const

// 单位映射
export const UNIT_NAMES = {
  token: 'tokens',
  character: '字符',
  seconds: '秒',
} as const

// 表单选项配置
export const FORM_OPTIONS = {
  // 服务模块选项
  SERVICE_MODULES: [
    { label: MODULE_NAMES[SERVICE_MODULE.LLM], value: SERVICE_MODULE.LLM },
    { label: MODULE_NAMES[SERVICE_MODULE.TTS], value: SERVICE_MODULE.TTS },
    { label: MODULE_NAMES[SERVICE_MODULE.ASR], value: SERVICE_MODULE.ASR },
  ] as ReadonlyArray<{ label: string, value: string }>,

  // 用户状态选项
  USER_STATUS: [
    { label: USER_STATUS_TEXT[USER_STATUS.ACTIVE], value: USER_STATUS.ACTIVE },
    { label: USER_STATUS_TEXT[USER_STATUS.BLOCKED], value: USER_STATUS.BLOCKED },
  ] as ReadonlyArray<{ label: string, value: string }>,

  // App 状态选项
  APP_STATUS: [
    { label: APP_STATUS_TEXT[APP_STATUS.OK], value: APP_STATUS.OK },
    { label: APP_STATUS_TEXT[APP_STATUS.BLOCKED], value: APP_STATUS.BLOCKED },
  ] as ReadonlyArray<{ label: string, value: string }>,

  // 货币选项
  CURRENCY: [
    { label: '人民币 (CNY)', value: 'CNY' },
    { label: '美元 (USD)', value: 'USD' },
  ] as ReadonlyArray<{ label: string, value: string }>,

  // 充值类型选项
  RECHARGE_TYPES: [
    { label: RECHARGE_TYPE_LABELS[RECHARGE_TYPE.ADMIN], value: RECHARGE_TYPE.ADMIN },
    { label: RECHARGE_TYPE_LABELS[RECHARGE_TYPE.ALIPAY], value: RECHARGE_TYPE.ALIPAY },
    { label: RECHARGE_TYPE_LABELS[RECHARGE_TYPE.WECHAT], value: RECHARGE_TYPE.WECHAT },
    { label: RECHARGE_TYPE_LABELS[RECHARGE_TYPE.BANK], value: RECHARGE_TYPE.BANK },
  ] as ReadonlyArray<{ label: string, value: string }>,

  // 用户类型选项
  USER_TYPE: [
    { label: '管理人员', value: 'admin' },
    { label: '普通用户', value: 'client' },
  ] as ReadonlyArray<{ label: string, value: string }>,
} as const

// 成功消息模板
export const SUCCESS_MESSAGES = {
  USER_CREATED: '用户创建成功！',
  USER_UPDATED: '用户信息更新成功！',
  USER_DELETED: '用户删除成功！',
  USER_BATCH_DELETED: '批量删除完成！',

  API_KEY_CREATED: 'API Key创建成功！',
  API_KEY_UPDATED: 'API Key更新成功！',
  API_KEY_DELETED: 'API Key删除成功！',
  API_KEY_BATCH_DELETED: '批量删除完成！',

  QUOTA_CREATED: '资源包创建成功！',
  QUOTA_UPDATED: '资源包更新成功！',

  RECHARGE_CREATED: '充值成功！',

  PRICING_CREATED: '价格规则创建成功！',
  PRICING_UPDATED: '价格规则更新成功！',
  PRICING_DELETED: '价格规则删除成功！',
  PRICING_BATCH_DELETED: '批量删除完成！',
} as const

// 确认消息模板
export const CONFIRM_MESSAGES = {
  DELETE_USER: '确定要删除这个用户吗？删除后无法恢复！',
  BATCH_DELETE_USERS: '确定要删除选中的用户吗？此操作不可撤销！',

  DELETE_API_KEY: '确定要删除这个API Key吗？删除后无法恢复！',
  BATCH_DELETE_API_KEYS: '确定要删除选中的API Key吗？此操作不可撤销！',

  DELETE_PRICING: '确定要删除这条价格规则吗？删除后将无法恢复！',
  BATCH_DELETE_PRICING: '确定要删除选中的价格规则吗？此操作不可撤销！',
} as const

// 计费说明文本
export const BILLING_DESCRIPTIONS = {
  MIXED: '混合计费：优先使用资源包，资源包用完后扣除用户余额',
  QUOTA_ONLY: '资源包计费：仅使用配置的资源包',
  BALANCE_ONLY: '余额计费：直接扣除用户余额',
  NO_BILLING: '无可用计费方式：请添加资源包或为用户充值',

  API_KEY_SYSTEM: '系统支持两种计费方式：①资源包计费（优先）- 从配置的资源包中扣减用量；②余额计费（兜底）- 当资源包不足时，从用户余额中扣费。',
  RECHARGE_SYSTEM: '用户余额用于在资源包不足时进行计费。管理员可以直接为用户充值，用户也可以通过第三方支付完成充值。',
} as const

export const BILLING_TYPE_LABELS = {
  [BILLING_TYPE.QUOTA]: '资源包计费',
  [BILLING_TYPE.BALANCE]: '余额计费',
} as const

export const BILLING_UNIT_LABELS = {
  [BILLING_UNIT.TOKENS]: 'Tokens',
  [BILLING_UNIT.CHARACTERS]: '字符',
  [BILLING_UNIT.SECONDS]: '秒',
} as const
