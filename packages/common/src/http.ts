import { useAxios } from '@uozi-admin/request'

const { setRequestInterceptor, setResponseInterceptor } = useAxios()

export function serviceInterceptor(handlers: {
  getToken?: () => string
  responseSuccessHandler?: (response: any) => void
  responseErrorHandler?: (error: any) => void
}) {
  return () => {
    setRequestInterceptor((config) => {
      if (handlers.getToken) {
        const token = handlers.getToken()
        if (token)
          config.headers.Token = token
      }

      return config
    })

    setResponseInterceptor(
      (response) => {
        handlers.responseSuccessHandler?.(response)

        return Promise.resolve(response.data)
      },
      async (error) => {
        handlers.responseErrorHandler?.(error)

        // Handle JSON error that comes back as Blob for blob request type
        if (error?.response?.data instanceof Blob && error?.response?.data?.type === 'application/json') {
          try {
            const text = await error.response.data.text()
            error.response.data = JSON.parse(text)
          }
          catch (e) {
          // If parsing fails, we'll continue with the original error.response.data

            console.error('Failed to parse blob error response as JSON', e)
          }
        }

        return Promise.reject(error.response?.data)
      },
    )
  }
}
