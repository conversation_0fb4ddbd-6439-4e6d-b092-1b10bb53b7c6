// 分页相关类型
export interface PaginationParams {
  page?: number
  page_size?: number
}

export interface PaginationResponse<T> {
  data: T[]
  pagination: {
    total: number
    per_page: number
    total_pages: number
    current_page: number
  }
}

// API 响应基础类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  success: boolean
}

// 错误类型
export interface ApiError {
  code: number
  message: string
  details?: any
}
