import type { ModelBase } from './base'
import type { User } from './user'

export type Unit = 'tokens' | 'characters' | 'seconds'

// 货币类型
export type Currency = 'CNY' | 'USD'

// App 状态类型
export type AppStatus = 'ok' | 'blocked'

// 配额状态类型
export type QuotaStatus = 'active' | 'expired' | 'exhausted' | 'disabled'

// 充值状态类型
export type RechargeStatus = 'completed' | 'pending' | 'failed' | 'cancelled'

// 充值类型
export type RechargeType = 'admin' | 'alipay' | 'wechat' | 'bank'

// 配额类型
export type QuotaType = 'admin' | 'purchase' | 'promotion' | 'gift'

// 资源包状态类型
export type QuotaPackageStatus = 'active' | 'expired' | 'exhausted' | 'disabled'

// 服务模块类型
export type ServiceModule = 'llm' | 'tts' | 'asr'

// 计费类型
export type BillingType = 'quota' | 'balance'

// App 信息
export interface App extends ModelBase {
  api_key: string
  name?: string
  module: ServiceModule
  user_id: string
  status: AppStatus
  comment?: string
  llm_quotas?: QuotaPackage[]
  tts_quotas?: QuotaPackage[]
  asr_quotas?: QuotaPackage[]
  user?: User
}

// 配额信息
export interface QuotaPackage extends ModelBase {
  user_id: string
  user?: User
  app_id?: string
  app?: App
  api_key?: string
  module: ServiceModule
  model_name?: string
  quota: number
  used: number
  available: number
  expires_at?: number
  status: QuotaPackageStatus
  type: QuotaType
  description?: string
  operator_id?: string
  operator?: User
}

// 用量历史
export interface UsageLog extends ModelBase {
  app_id?: string
  app?: App
  api_key: string
  module: ServiceModule
  model_name?: string
  usage: number
  unit_price: number
  cost: number
  currency: Currency
  unit: Unit
  billing_type: BillingType
  quota_package_id?: string
  user_id?: string
  metadata?: Record<string, any>
}

// 定价规则
export interface PricingRule extends ModelBase {
  module: ServiceModule
  model_name: string
  unit_price: number
  currency: Currency
  unit: Unit
  base_unit: number
  is_active: boolean
  priority: number
  description: string
}

// 充值记录接口
export interface RechargeRecord extends ModelBase {
  app_id?: string
  app?: App
  api_key?: string
  user_id: string
  user?: User
  amount: number
  type: RechargeType
  status: RechargeStatus
  trade_no?: string
  description?: string
  operator_id?: string
  operator?: User
}
