import type { ModelBase } from './base'

export type UserStatus = 'active' | 'blocked'
export type UserType = 'admin' | 'client'

export interface User extends ModelBase {
  id: string
  name: string
  email: string
  avatar: string
  phone: string
  status: UserStatus
  balance: number // 用户余额
  last_active: string
  avatar_id?: string
  user_type: UserType
}

export interface LoginRequest {
  email: string
  password: string
}

export interface LoginResponse {
  token: string
  user: User
}
