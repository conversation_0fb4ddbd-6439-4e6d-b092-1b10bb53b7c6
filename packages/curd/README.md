# @billing/curd - 现代化 Vue 3 CURD 组件库

一个基于 Vue 3 + TypeScript + TanStack Table 的现代化 CURD 组件库，内置字段驱动配置和自动字段复用功能，大幅减少配置代码量。

## 📖 目录

- [✨ 核心特性](#-核心特性)
- [🚀 快速开始](#-快速开始)
- [📝 支持的字段类型](#-支持的字段类型)
- [🎨 自定义渲染功能](#-自定义渲染功能)
- [🔧 高级功能](#-高级功能)
- [🌟 完整示例](#-完整示例)
- [📚 API 参考](#-api-参考)
- [🔗 字段联动功能](#-字段联动功能)
- [🧩 组合函数使用](#-组合函数使用)
- [🎯 最佳实践](#-最佳实践)
- [🎯 常见使用场景](#-常见使用场景)
- [🔧 组件配置](#-组件配置)
- [🔧 开发和贡献](#-开发和贡献)
- [⚡ 性能优化](#-性能优化)
- [🐛 故障排除](#-故障排除)
- [🤝 贡献指南](#-贡献指南)

## ✨ 核心特性

- 🚀 **字段驱动配置** - 一份字段配置搞定表格、表单、搜索三大场景
- 📱 **响应式设计** - 基于 shadcn-vue 组件库，支持移动端适配
- 🎯 **TypeScript 支持** - 完整的类型定义，提供优秀的开发体验
- 🔧 **高度可定制** - 支持插槽、自定义渲染、钩子函数等扩展方式
- ⚡ **性能优化** - 基于 TanStack Table 的高性能表格，支持虚拟滚动
- 🎨 **现代化 UI** - 美观的界面设计，支持暗色模式
- 🔗 **字段联动** - 内置字段联动功能，支持复杂的表单交互
- 🛡️ **表单验证** - 内置表单验证系统，支持自定义验证规则
- 🌐 **国际化支持** - 内置 i18n 支持，轻松实现多语言

## 🚀 快速开始

### 安装

```bash
pnpm add @billing/curd
```

### 基础用法

```vue
<template>
  <StdCurd :config="curdConfig" />
</template>

<script setup lang="ts">
import { StdCurd, createCosyApi, type CurdConfig } from '@billing/curd'

// 定义数据类型
interface User {
  id: number
  username: string
  email: string
  role: string
  status: boolean
  created_at: string
}

// 创建 API 实例
const userApi = createCosyApi<User>({
  baseURL: '/api',
  resource: 'users'
})

// 配置 CURD
const curdConfig: CurdConfig<User> = {
  title: '用户管理',
  api: userApi,
  fields: [
    {
      key: 'id',
      label: 'ID',
      type: 'text',
      table: { show: true, sortable: true },
      form: { show: false },
      search: { show: false }
    },
    {
      key: 'username',
      label: '用户名',
      type: 'text',
      table: { show: true },
      form: { show: true, required: true },
      search: { show: true, placeholder: '请输入用户名' }
    },
    {
      key: 'email',
      label: '邮箱',
      type: 'email',
      table: { show: true },
      form: { show: true, required: true },
      search: { show: true }
    },
    {
      key: 'role',
      label: '角色',
      type: 'select',
      options: [
        { label: '管理员', value: 'admin' },
        { label: '普通用户', value: 'user' }
      ],
      table: { show: true },
      form: { show: true, required: true },
      search: { show: true }
    },
    {
      key: 'status',
      label: '状态',
      type: 'switch',
      table: { show: true },
      form: { show: true },
      search: { show: false }
    }
  ],
  actions: {
    add: true,
    edit: true,
    delete: true,
    batchDelete: true
  }
}
</script>
```

## 📝 支持的字段类型

### 基础输入类型

| 类型 | 描述 | 示例 |
|------|------|------|
| `text` | 基础文本输入 | 姓名、标题 |
| `email` | 邮箱输入 | 用户邮箱 |
| `password` | 密码输入 | 登录密码 |
| `url` | URL 输入 | 网站地址 |
| `tel` | 电话号码输入 | 联系电话 |
| `search` | 搜索输入 | 搜索关键词 |
| `number` | 数字输入 | 年龄、价格 |
| `number-field` | 数字字段组件 | 带步进器的数字输入 |
| `textarea` | 多行文本 | 描述、备注 |
| `rich-text` | 富文本编辑器 | 文章内容 |

### 选择类型

| 类型 | 描述 | 示例 |
|------|------|------|
| `select` | 下拉选择 | 部门、分类 |
| `combobox` | 可搜索下拉选择 | 城市选择 |
| `multiselect` | 多选下拉 | 标签、权限 |
| `checkbox` | 单个复选框 | 同意条款 |
| `checkbox-group` | 复选框组 | 多选项 |
| `radio` | 单个单选框 | 性别选择 |
| `radio-group` | 单选框组 | 单选项 |

### 开关和滑块类型

| 类型 | 描述 | 示例 |
|------|------|------|
| `switch` | 开关 | 启用/禁用 |
| `slider` | 滑块 | 音量调节 |
| `range-slider` | 范围滑块 | 价格区间 |

### 日期时间类型

| 类型 | 描述 | 示例 |
|------|------|------|
| `date` | 日期选择 | 生日、入职日期 |
| `datetime` | 日期时间选择 | 创建时间 |
| `time` | 时间选择 | 时间设置 |
| `calendar` | 日历选择器 | 日程安排 |
| `date-range` | 日期范围选择 | 统计时间段 |
| `date-picker` | 弹出式日期选择 | 快速日期选择 |

### 特殊输入类型

| 类型 | 描述 | 示例 |
|------|------|------|
| `tags-input` | 标签输入 | 关键词标签 |
| `pin-input` | PIN 码输入 | 验证码 |
| `file` | 文件上传 | 文档上传 |
| `file-multiple` | 多文件上传 | 批量文件上传 |
| `image` | 图片上传 | 头像上传 |
| `color` | 颜色选择器 | 主题色选择 |

## 🎨 自定义渲染功能

StdCurd 组件支持完全自定义的列和表头渲染，让您可以根据业务需求灵活定制表格的显示效果。

### 功能特性

- ✅ **表头自定义渲染** - 为任意列自定义表头显示
- ✅ **单元格自定义渲染** - 为任意列自定义单元格内容
- ✅ **操作列自定义** - 完全自定义操作按钮
- ✅ **工具栏自定义** - 自定义工具栏左右两侧内容
- ✅ **完整的插槽参数** - 提供丰富的上下文数据
- ✅ **类型安全** - 完整的 TypeScript 类型支持

### 插槽系统

#### 1. 表头自定义渲染

**插槽名格式：** `header-{columnId}`

```vue
<template>
  <StdCurd :config="config">
    <!-- 为 name 列自定义表头 -->
    <template #header-name="{ header, column, context }">
      <div class="flex items-center gap-2">
        <User class="w-4 h-4" />
        用户名
      </div>
    </template>

    <!-- 为 status 列添加说明 -->
    <template #header-status>
      <div class="text-center">
        <div>状态</div>
        <div class="text-xs text-muted-foreground">
          (启用/禁用)
        </div>
      </div>
    </template>
  </StdCurd>
</template>
```

#### 2. 单元格自定义渲染

**插槽名格式：** `cell-{columnId}`

```vue
<template>
  <StdCurd :config="config">
    <!-- 用户名显示头像 -->
    <template #cell-name="{ value, row }">
      <div class="flex items-center gap-3">
        <Avatar class="w-8 h-8">
          <AvatarImage :src="row.avatar" />
          <AvatarFallback>{{ value?.charAt(0)?.toUpperCase() }}</AvatarFallback>
        </Avatar>
        <div>
          <div class="font-medium">
            {{ value }}
          </div>
          <div class="text-sm text-muted-foreground">
            {{ row.email }}
          </div>
        </div>
      </div>
    </template>

    <!-- 状态显示徽章 -->
    <template #cell-status="{ value }">
      <Badge :variant="value ? 'default' : 'secondary'">
        <div class="flex items-center gap-1">
          <div
            class="w-2 h-2 rounded-full"
            :class="value ? 'bg-green-500' : 'bg-gray-400'"
          />
          {{ value ? '启用' : '禁用' }}
        </div>
      </Badge>
    </template>

    <!-- 时间格式化 -->
    <template #cell-created_at="{ value }">
      <div class="text-sm">
        <div>{{ formatDate(value) }}</div>
        <div class="text-muted-foreground">
          {{ formatTimeAgo(value) }}
        </div>
      </div>
    </template>
  </StdCurd>
</template>
```

#### 3. 操作列自定义

**插槽名：** `actions`

```vue
<template>
  <StdCurd :config="config">
    <!-- 完全自定义操作列 -->
    <template #actions="{ row }">
      <div class="flex items-center justify-end gap-1">
        <Button
          variant="ghost"
          size="sm"
          @click="viewUser(row)"
        >
          <Eye class="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          @click="editUser(row)"
        >
          <Edit class="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          @click="resetPassword(row)"
        >
          <Key class="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          class="text-destructive"
          @click="deleteUser(row)"
        >
          <Trash2 class="w-4 h-4" />
        </Button>
      </div>
    </template>
  </StdCurd>
</template>
```

#### 4. 工具栏自定义

**插槽名：** `toolbar-left` | `toolbar-right`

```vue
<template>
  <StdCurd :config="config">
    <!-- 自定义工具栏左侧 -->
    <template #toolbar-left>
      <Button
        size="sm"
        @click="exportData"
      >
        <Download class="w-4 h-4 mr-2" />
        导出数据
      </Button>
      <Button
        variant="outline"
        size="sm"
        @click="importData"
      >
        <Upload class="w-4 h-4 mr-2" />
        导入数据
      </Button>
    </template>

    <!-- 自定义工具栏右侧 -->
    <template #toolbar-right>
      <div class="flex items-center gap-2">
        <Input
          v-model="searchKeyword"
          placeholder="搜索..."
          class="w-48"
        />
        <Select v-model="statusFilter">
          <SelectTrigger class="w-32">
            <SelectValue placeholder="状态筛选" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">
              全部
            </SelectItem>
            <SelectItem value="active">
              启用
            </SelectItem>
            <SelectItem value="inactive">
              禁用
            </SelectItem>
          </SelectContent>
        </Select>
      </div>
    </template>
  </StdCurd>
</template>
```

### 常用渲染模式

#### 图片显示

```vue
<template #cell-avatar="{ value, row }">
  <Avatar class="w-10 h-10">
    <AvatarImage
      :src="value"
      :alt="row.name"
    />
    <AvatarFallback>{{ row.name?.charAt(0) }}</AvatarFallback>
  </Avatar>
</template>
```

#### 状态指示器

```vue
<template #cell-status="{ value }">
  <div class="flex items-center gap-2">
    <div
      class="w-2 h-2 rounded-full"
      :class="{
        'bg-green-500': value === 'active',
        'bg-red-500': value === 'inactive',
        'bg-yellow-500': value === 'pending',
      }"
    />
    <span class="capitalize">{{ value }}</span>
  </div>
</template>
```

#### 进度条

```vue
<template #cell-progress="{ value }">
  <div class="w-full">
    <div class="flex justify-between text-sm mb-1">
      <span>进度</span>
      <span>{{ value }}%</span>
    </div>
    <Progress
      :value="value"
      class="h-2"
    />
  </div>
</template>
```

#### 标签组合

```vue
<template #cell-tags="{ value }">
  <div class="flex flex-wrap gap-1">
    <Badge
      v-for="tag in value"
      :key="tag"
      variant="secondary"
      class="text-xs"
    >
      {{ tag }}
    </Badge>
  </div>
</template>
```

## 🔧 高级功能

### 字段联动配置

支持复杂的字段联动逻辑，包括选项联动、值联动和状态联动：

```typescript
const config: CurdConfig = {
  // ... 其他配置
  fields: [
    {
      key: 'province',
      label: '省份',
      type: 'select',
      options: [
        { label: '广东省', value: 'guangdong' },
        { label: '江苏省', value: 'jiangsu' }
      ],
      form: { show: true, required: true }
    },
    {
      key: 'city',
      label: '城市',
      type: 'select',
      form: { show: true, required: true },
      // 字段联动配置
      linkage: {
        trigger: 'province', // 监听省份字段变化
        action: 'updateOptions', // 更新选项
        handler: async (triggerValue, formData) => {
          // 根据省份获取城市列表
          const cities = await getCitiesByProvince(triggerValue)
          return cities.map(city => ({ label: city.name, value: city.id }))
        }
      }
    }
  ]
}
```

### 条件字段显示

```typescript
{
  key: 'vip_price',
  label: 'VIP价格',
  type: 'number',
  form: {
    show: true,
    // 条件显示：只在VIP分类时显示
    show: (formData) => formData.category === 'vip'
  }
}
```

### 动态禁用字段

```typescript
{
  key: 'code',
  label: '产品编码',
  type: 'text',
  form: {
    show: true,
    required: true,
    // 编辑时禁用
    disabled: (formData) => formData.id !== undefined
  }
}
```

### 自定义验证规则

```typescript
{
  key: 'username',
  label: '用户名',
  type: 'text',
  form: {
    show: true,
    required: true,
    rules: [
      { required: true, message: '用户名必填' },
      { min: 3, max: 20, message: '用户名长度在3-20个字符' },
      {
        validator: (value, formData) => {
          if (value.includes('admin')) {
            return '用户名不能包含admin'
          }
          return true
        }
      }
    ]
  }
}
```

### 生命周期钩子

```typescript
const config: CurdConfig = {
  api: userApi,
  title: '用户管理',
  fields: [...],
  hooks: {
    // 列表相关
    beforeList: async (params) => {
      console.log('获取列表前:', params)
      return params
    },
    afterList: async (response) => {
      console.log('获取列表后:', response)
    },

    // 创建相关
    beforeCreate: async (data) => {
      // 创建前处理数据
      return { ...data, createdBy: 'admin', createdAt: new Date() }
    },
    afterCreate: async (result) => {
      console.log('创建成功:', result)
      // 可以在这里执行额外的操作，如发送通知
    },

    // 更新相关
    beforeUpdate: async (id, data) => {
      return { ...data, updatedAt: new Date() }
    },
    afterUpdate: async (result) => {
      console.log('更新成功:', result)
    },

    // 删除相关
    beforeDelete: async (id) => {
      console.log('删除前确认:', id)
    },
    afterDelete: async (id) => {
      console.log('删除成功:', id)
    },

    // 表单相关
    beforeFormSubmit: async (formData, mode) => {
      console.log('表单提交前:', formData, mode)
      return formData
    },
    afterFormSubmit: async (result, mode) => {
      console.log('表单提交后:', result, mode)
    },

    // 错误处理
    onError: async (error, operation, context) => {
      console.error(`操作失败 [${operation}]:`, error)
      // 可以在这里统一处理错误，如显示错误提示
    }
  }
}
```

## 🌟 完整示例

以下是一个完整的产品管理系统示例：

```vue
<template>
  <div class="p-6">
    <StdCurd :config="config">
      <!-- 自定义状态列渲染 -->
      <template #cell-status="{ value }">
        <Badge :variant="value ? 'default' : 'secondary'">
          <div class="flex items-center gap-1">
            <div
              class="w-2 h-2 rounded-full"
              :class="value ? 'bg-green-500' : 'bg-gray-400'"
            />
            {{ value ? '上架' : '下架' }}
          </div>
        </Badge>
      </template>

      <!-- 自定义价格列渲染 -->
      <template #cell-price="{ value }">
        <span class="font-medium text-green-600">
          ¥{{ value.toFixed(2) }}
        </span>
      </template>

      <!-- 自定义操作列 -->
      <template #actions="{ row }">
        <div class="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            @click="viewProduct(row)"
          >
            <Eye class="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            @click="editProduct(row)"
          >
            <Edit class="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            class="text-destructive"
            @click="deleteProduct(row)"
          >
            <Trash2 class="w-4 h-4" />
          </Button>
        </div>
      </template>
    </StdCurd>
  </div>
</template>

<script setup lang="ts">
import { StdCurd, createCosyApi, type CurdConfig } from '@billing/curd'
import { Badge, Button } from '@billing/ui'
import { Eye, Edit, Trash2 } from 'lucide-vue-next'

interface Product {
  id: number
  name: string
  description: string
  price: number
  category: string
  status: boolean
  created_at: string
}

const productApi = createCosyApi<Product>({
  baseURL: '/api',
  resource: 'products'
})

const config: CurdConfig<Product> = {
  title: '产品管理',
  api: productApi,
  fields: [
    {
      key: 'id',
      label: 'ID',
      type: 'text',
      table: { show: true, sortable: true },
      form: { show: false },
      search: { show: false }
    },
    {
      key: 'name',
      label: '产品名称',
      type: 'text',
      table: { show: true, sortable: true },
      form: {
        show: true,
        required: true,
        rules: [
          { required: true, message: '产品名称必填' },
          { min: 2, max: 50, message: '长度在2-50个字符' }
        ]
      },
      search: { show: true, placeholder: '请输入产品名称' }
    },
    {
      key: 'description',
      label: '产品描述',
      type: 'textarea',
      table: { show: false },
      form: {
        show: true,
        placeholder: '请输入产品详细描述'
      },
      search: { show: false }
    },
    {
      key: 'price',
      label: '价格',
      type: 'number',
      table: { show: true, sortable: true },
      form: {
        show: true,
        required: true,
        rules: [
          { required: true, message: '价格必填' },
          { min: 0, message: '价格不能为负数' }
        ]
      },
      search: { show: false }
    },
    {
      key: 'category',
      label: '分类',
      type: 'select',
      options: [
        { label: '电子产品', value: 'electronics' },
        { label: '服装', value: 'clothing' },
        { label: '家居', value: 'home' },
        { label: '图书', value: 'books' }
      ],
      table: { show: true },
      form: { show: true, required: true },
      search: { show: true }
    },
    {
      key: 'status',
      label: '上架状态',
      type: 'switch',
      table: { show: true },
      form: { show: true },
      search: { show: false }
    },
    {
      key: 'created_at',
      label: '创建时间',
      type: 'datetime',
      table: { show: true, sortable: true },
      form: { show: false },
      search: { show: false }
    }
  ],
  actions: {
    add: true,
    edit: true,
    delete: true,
    batchDelete: true
  },
  confirmMessages: {
    delete: '确定要删除这个产品吗？删除后将无法恢复！',
    batchDelete: '确定要删除选中的产品吗？'
  },
  successMessages: {
    create: '产品创建成功！',
    update: '产品更新成功！',
    delete: '产品删除成功！',
    batchDelete: '批量删除完成！'
  },
  hooks: {
    beforeCreate: async (data) => {
      return { ...data, created_at: new Date().toISOString() }
    },
    afterCreate: async (result) => {
      console.log('产品创建成功:', result)
    },
    onError: async (error, operation) => {
      console.error(`操作失败 [${operation}]:`, error)
    }
  }
}

// 自定义操作方法
function viewProduct(product: Product) {
  console.log('查看产品:', product)
}

function editProduct(product: Product) {
  console.log('编辑产品:', product)
}

function deleteProduct(product: Product) {
  console.log('删除产品:', product)
}
</script>
```

## 📚 API 参考

### CurdApi 接口

```typescript
interface CurdApi<T> {
  // 获取列表数据
  getList: (params?: ListParams) => Promise<ListResponse<T>>
  // 获取单个资源
  getItem: (id: string | number) => Promise<T>
  // 创建资源
  createItem: (data: Partial<T>) => Promise<T>
  // 更新资源
  updateItem: (id: string | number, data: Partial<T>) => Promise<T>
  // 删除资源
  deleteItem: (id: string | number, query?: Record<string, any>) => Promise<T>
  // 恢复资源
  restoreItem: (id: string | number) => Promise<T>
}

// 列表查询参数
interface ListParams {
  page?: number
  per_page?: number
  [key: string]: any
}

// 列表响应数据
interface ListResponse<T> {
  data: T[]
  pagination: {
    current_page: number
    per_page: number
    total_pages: number
    total: number
  }
}
```

### CurdHooks 钩子函数

```typescript
interface CurdHooks<T> {
  // 列表相关
  beforeList?: (params: ListParams) => ListParams | Promise<ListParams> | false
  afterList?: (response: ListResponse<T>) => void | Promise<void> | false

  // 创建相关
  beforeCreate?: (data: Partial<T>) => Partial<T> | Promise<Partial<T>> | false
  afterCreate?: (data: T) => void | Promise<void> | false

  // 更新相关
  beforeUpdate?: (id: string | number, data: Partial<T>) => Partial<T> | Promise<Partial<T>> | false
  afterUpdate?: (data: T) => void | Promise<void> | false

  // 删除相关
  beforeDelete?: (id: string | number) => void | Promise<void> | false
  afterDelete?: (id: string | number) => void | Promise<void> | false

  // 批量删除相关
  beforeBatchDelete?: (ids: (string | number)[]) => void | Promise<void> | false
  afterBatchDelete?: (ids: (string | number)[]) => void | Promise<void> | false

  // 表单相关
  beforeFormSubmit?: (formData: Record<string, any>, mode: 'create' | 'update') => Record<string, any> | Promise<Record<string, any>> | false
  afterFormSubmit?: (result: T, mode: 'create' | 'update') => void | Promise<void> | false

  // 搜索相关
  beforeSearch?: (searchParams: Record<string, any>) => Record<string, any> | Promise<Record<string, any>> | false
  afterSearch?: (searchParams: Record<string, any>, results: T[]) => void | Promise<void> | false

  // 错误处理
  onError?: (error: Error, operation: string, context?: any) => void | Promise<void>
}
```

### createCosyApi 工厂函数

```typescript
interface CreateApiOptions {
  baseURL?: string // API 基础地址
  axios?: AxiosInstance // 自定义 axios 实例
  resource: string // 资源名称
}

function createCosyApi<T>(options: CreateApiOptions): CurdApi<T>
```

### useCurd 组合函数

```typescript
interface UseCurdOptions<T> {
  api: CurdApi<T>
  columns: StdColumn<T>[]
  pageSize?: number
  pageSizeOptions?: number[]
  searchable?: boolean
  creatable?: boolean
  editable?: boolean
  deletable?: boolean
  batchDeletable?: boolean
  hooks?: CurdHooks<T>
  immediate?: boolean
  primaryKey?: string
}

interface UseCurdReturn<T> {
  // 数据状态
  data: Ref<T[]>
  loading: Ref<boolean>
  error: Ref<Error | null>
  total: Ref<number>
  currentPage: Ref<number>
  currentPageSize: Ref<number>
  totalPages: Ref<number>

  // 选中状态
  selectedRows: Ref<T[]>
  selectedRowKeys: Ref<(string | number)[]>

  // 搜索和排序状态
  searchParams: Ref<Record<string, any>>
  sortParams: Ref<Record<string, any>>

  // 操作方法
  refresh: () => Promise<void>
  search: (params: Record<string, any>) => Promise<void>
  sort: (field: string, order: 'asc' | 'desc') => Promise<void>
  changePage: (page: number) => Promise<void>
  changePageSize: (size: number) => Promise<void>
  create: (data: Partial<T>) => Promise<T>
  update: (id: string | number, data: Partial<T>) => Promise<T>
  remove: (id: string | number, permanently?: boolean) => Promise<void>
  batchRemove: (ids: (string | number)[], permanently?: boolean) => Promise<void>
  clearSelection: () => void
}

function useCurd<T>(options: UseCurdOptions<T>): UseCurdReturn<T>
```

## 🔗 字段联动功能

### 基础联动配置

字段联动允许根据其他字段的值动态更新当前字段的选项、状态或值：

```typescript
const config: CurdConfig = {
  fields: [
    {
      key: 'category',
      label: '商品分类',
      type: 'select',
      options: [
        { label: '电子产品', value: 'electronics' },
        { label: '服装', value: 'clothing' }
      ],
      form: { show: true, required: true }
    },
    {
      key: 'subcategory',
      label: '子分类',
      type: 'select',
      form: { show: true, required: true },
      // 字段联动配置
      linkage: {
        trigger: 'category', // 监听分类字段变化
        action: 'updateOptions', // 更新选项
        handler: async (categoryValue, formData) => {
          // 根据分类获取子分类选项
          const subcategories = await getSubcategoriesByCategory(categoryValue)
          return subcategories.map(item => ({
            label: item.name,
            value: item.id
          }))
        }
      }
    }
  ]
}
```

### 高级联动功能

```typescript
// 多字段联动
{
  key: 'price',
  label: '价格',
  type: 'number',
  form: { show: true, required: true },
  linkage: {
    trigger: ['category', 'brand'], // 监听多个字段
    action: 'updateValue', // 更新值
    handler: async (triggerValues, formData) => {
      const [category, brand] = triggerValues
      // 根据分类和品牌计算建议价格
      const suggestedPrice = await calculatePrice(category, brand)
      return suggestedPrice
    }
  }
}

// 条件显示联动
{
  key: 'warranty_period',
  label: '保修期',
  type: 'select',
  options: [
    { label: '1年', value: 12 },
    { label: '2年', value: 24 },
    { label: '3年', value: 36 }
  ],
  form: {
    show: true,
    // 只有电子产品才显示保修期
    show: (formData) => formData.category === 'electronics'
  }
}

// 状态联动
{
  key: 'shipping_method',
  label: '配送方式',
  type: 'select',
  options: [
    { label: '普通快递', value: 'standard' },
    { label: '加急配送', value: 'express' },
    { label: '同城配送', value: 'local' }
  ],
  form: {
    show: true,
    // 根据商品重量禁用某些配送方式
    disabled: (formData) => formData.weight > 10 // 超过10kg禁用
  }
}
```

### 字段联动类型

| 联动类型 | 说明 | 使用场景 |
|---------|------|----------|
| `updateOptions` | 更新字段选项 | 省市联动、分类子分类联动 |
| `updateValue` | 更新字段值 | 自动计算价格、设置默认值 |
| `updateState` | 更新字段状态 | 动态禁用、显示隐藏 |
| `validate` | 触发验证 | 关联字段验证 |

## 🧩 组合函数使用

### useCurd 核心组合函数

```vue
<script setup lang="ts">
import { useCurd, createCosyApi } from '@billing/curd'

const api = createCosyApi({ resource: 'users' })

const curd = useCurd({
  api,
  columns: [], // TanStack Table 列配置
  pageSize: 20,
  searchable: true,
  creatable: true,
  editable: true,
  deletable: true,
  hooks: {
    beforeCreate: async (data) => {
      return { ...data, created_at: new Date() }
    }
  }
})

// 解构使用
const {
  data,
  loading,
  total,
  refresh,
  create,
  update,
  remove
} = curd
</script>
```

### useFormField 表单字段处理

```vue
<script setup lang="ts">
import { useFormField } from '@billing/curd'

const props = defineProps<{
  field: FormFieldConfig
  modelValue: any
  disabled?: boolean
  error?: string
}>()

const {
  showRequired,
  isVisible,
  isDisabled,
  getFieldProps
} = useFormField(props)

const fieldProps = getFieldProps(props.field)
</script>
```

### useFieldLinkage 字段联动处理

```vue
<script setup lang="ts">
import { useFieldLinkage } from '@billing/curd'

const fields = ref([...]) // 字段配置
const formData = reactive({}) // 表单数据

const {
  fieldStates,
  onFieldChange,
  getFieldOptions,
  getFieldState
} = useFieldLinkage(fields, formData)

// 字段值变化时调用
function handleFieldChange(field, value) {
  formData[field.key] = value
  onFieldChange(field.key, value, formData)
}
</script>
```

## 📊 自定义渲染插槽参数

### 表头插槽参数

```typescript
interface HeaderSlotProps {
  header: any // TanStack Table 的 Header 对象
  column: any // TanStack Table 的 Column 对象
  context: any // Header 上下文
}
```

### 单元格插槽参数

```typescript
interface CellSlotProps<T> {
  cell: any // TanStack Table 的 Cell 对象
  row: T // 当前行的完整数据
  value: any // 当前单元格的值
  index: number // 行索引
  column: any // TanStack Table 的 Column 对象
  context: any // Cell 上下文
}
```

### 操作列插槽参数

```typescript
interface ActionsSlotProps<T> {
  row: T // 当前行的完整数据
  index: number // 行索引
}
```

## 🎯 最佳实践

### 1. 渐进式开发

1. **先用智能推断**：让 EasyCurd 自动推断，然后根据需要调整
2. **渐进式增强**：从最简配置开始，逐步添加自定义
3. **合理的字段分组**：相关字段放在一起
4. **清晰的标签和提示**：提供友好的用户体验

### 3. 性能优化

- 避免在模板中进行复杂计算，使用计算属性
- 为循环渲染的元素提供唯一的 `key`
- 使用 `v-memo` 优化静态内容

```vue
<template #cell-roles="{ value }">
  <div class="flex flex-wrap gap-1">
    <Badge
      v-for="role in value"
      :key="`${role.id}-${role.name}`"
      v-memo="[role.id, role.name]"
      variant="outline"
    >
      {{ role.name }}
    </Badge>
  </div>
</template>
```

### 4. 响应式设计

考虑不同屏幕尺寸的显示效果：

```vue
<template #cell-description="{ value }">
  <div class="max-w-xs truncate md:max-w-sm lg:max-w-md">
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger class="text-left">
          {{ value }}
        </TooltipTrigger>
        <TooltipContent>
          <p class="max-w-xs">
            {{ value }}
          </p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  </div>
</template>
```

### 5. 无障碍支持

为操作按钮提供明确的标签：

```vue
<template #actions="{ row }">
  <div class="flex items-center gap-1">
    <Button
      variant="ghost"
      size="sm"
      :aria-label="`编辑 ${row.name}`"
      @click="editUser(row)"
    >
      <Edit class="w-4 h-4" />
      <span class="sr-only">编辑</span>
    </Button>
  </div>
</template>
```

## 🎯 常见使用场景

### 1. 用户管理系统

```typescript
const userConfig: CurdConfig<User> = {
  title: '用户管理',
  api: createCosyApi({ resource: 'users' }),
  fields: [
    {
      key: 'avatar',
      label: '头像',
      type: 'image',
      table: { show: true },
      form: { show: true }
    },
    {
      key: 'username',
      label: '用户名',
      type: 'text',
      table: { show: true, sortable: true },
      form: { show: true, required: true },
      search: { show: true }
    },
    {
      key: 'email',
      label: '邮箱',
      type: 'email',
      table: { show: true },
      form: { show: true, required: true },
      search: { show: true }
    },
    {
      key: 'role',
      label: '角色',
      type: 'select',
      options: [
        { label: '超级管理员', value: 'super_admin' },
        { label: '管理员', value: 'admin' },
        { label: '普通用户', value: 'user' }
      ],
      table: { show: true },
      form: { show: true, required: true },
      search: { show: true }
    },
    {
      key: 'status',
      label: '状态',
      type: 'switch',
      table: { show: true },
      form: { show: true }
    }
  ],
  actions: {
    add: true,
    edit: true,
    delete: true,
    batchDelete: true
  }
}
```

### 2. 商品管理系统

```typescript
const productConfig: CurdConfig<Product> = {
  title: '商品管理',
  api: createCosyApi({ resource: 'products' }),
  fields: [
    {
      key: 'image',
      label: '商品图片',
      type: 'image',
      table: { show: true },
      form: { show: true, required: true }
    },
    {
      key: 'name',
      label: '商品名称',
      type: 'text',
      table: { show: true, sortable: true },
      form: { show: true, required: true },
      search: { show: true }
    },
    {
      key: 'category_id',
      label: '商品分类',
      type: 'select',
      table: { show: true },
      form: { show: true, required: true },
      search: { show: true },
      // 远程加载分类选项
      dynamicOptions: async () => {
        const categories = await getCategoriesList()
        return categories.map(cat => ({
          label: cat.name,
          value: cat.id
        }))
      }
    },
    {
      key: 'price',
      label: '价格',
      type: 'number',
      table: { show: true, sortable: true },
      form: {
        show: true,
        required: true,
        rules: [
          { min: 0, message: '价格不能为负数' }
        ]
      }
    },
    {
      key: 'stock',
      label: '库存',
      type: 'number',
      table: { show: true, sortable: true },
      form: {
        show: true,
        required: true,
        rules: [
          { min: 0, message: '库存不能为负数' }
        ]
      }
    },
    {
      key: 'status',
      label: '上架状态',
      type: 'switch',
      table: { show: true },
      form: { show: true }
    }
  ]
}
```

### 3. 订单管理系统

```typescript
const orderConfig: CurdConfig<Order> = {
  title: '订单管理',
  api: createCosyApi({ resource: 'orders' }),
  fields: [
    {
      key: 'order_no',
      label: '订单号',
      type: 'text',
      table: { show: true, sortable: true },
      form: { show: false }, // 订单号自动生成，不在表单中显示
      search: { show: true, placeholder: '请输入订单号' }
    },
    {
      key: 'customer_name',
      label: '客户姓名',
      type: 'text',
      table: { show: true },
      form: { show: true, required: true },
      search: { show: true }
    },
    {
      key: 'total_amount',
      label: '订单金额',
      type: 'number',
      table: { show: true, sortable: true },
      form: { show: true, required: true }
    },
    {
      key: 'status',
      label: '订单状态',
      type: 'select',
      options: [
        { label: '待付款', value: 'pending' },
        { label: '已付款', value: 'paid' },
        { label: '已发货', value: 'shipped' },
        { label: '已完成', value: 'completed' },
        { label: '已取消', value: 'cancelled' }
      ],
      table: { show: true },
      form: { show: true, required: true },
      search: { show: true }
    },
    {
      key: 'created_at',
      label: '下单时间',
      type: 'datetime',
      table: { show: true, sortable: true },
      form: { show: false },
      search: { show: true, searchType: 'range' }
    }
  ],
  actions: {
    add: true,
    edit: true,
    delete: false, // 订单通常不允许删除
    batchDelete: false
  },
  hooks: {
    beforeCreate: async (data) => {
      // 自动生成订单号
      return {
        ...data,
        order_no: generateOrderNo(),
        created_at: new Date()
      }
    }
  }
}
```

## 🔧 开发和贡献

### 本地开发

```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 运行测试
pnpm test

# 构建
pnpm build
```

### 项目结构

```
packages/curd/src/
├── api/                    # API 相关
│   └── index.ts           # createCosyApi 工厂函数
├── components/            # 组件
│   ├── fields/           # 字段组件
│   ├── StdCurd.vue       # 主组件
│   ├── StdForm.vue       # 表单组件
│   ├── StdTable.vue      # 表格组件
│   └── ...
├── composables/          # 组合函数
│   ├── useCurd.ts        # 核心 CURD 逻辑
│   ├── useFormField.ts   # 表单字段处理
│   ├── useFieldLinkage.ts # 字段联动
│   └── ...
├── types/                # 类型定义
│   ├── api.ts           # API 相关类型
│   ├── config.ts        # 配置相关类型
│   ├── form.ts          # 表单相关类型
│   └── ...
├── utils/                # 工具函数
│   ├── field-config-parser.ts # 字段配置解析
│   ├── error-handler.ts       # 错误处理
│   └── ...
└── index.ts              # 入口文件
```

### 核心设计理念

1. **字段驱动** - 通过统一的字段配置驱动表格、表单、搜索三大场景
2. **类型安全** - 完整的 TypeScript 类型定义，提供优秀的开发体验
3. **高度可扩展** - 支持插槽、自定义渲染、钩子函数等多种扩展方式
4. **性能优化** - 基于 TanStack Table，支持大数据量场景
5. **现代化** - 基于 Vue 3 Composition API 和 shadcn-vue 组件库

## ⚡ 性能优化

### 1. 大数据量优化

```typescript
// 使用 useOptimizedCurd 处理大数据量场景
import { useOptimizedCurd } from '@billing/curd'

const curd = useOptimizedCurd({
  api: userApi,
  // 启用虚拟滚动
  virtualScrolling: true,
  // 分页大小
  pageSize: 50,
  // 预加载页数
  preloadPages: 2,
  // 缓存策略
  cacheStrategy: 'memory'
})
```

### 2. 字段联动优化

```typescript
// 使用防抖优化联动性能
{
  key: 'search_keyword',
  label: '搜索关键词',
  type: 'text',
  linkage: {
    trigger: 'category',
    action: 'updateOptions',
    // 防抖延迟
    debounce: 300,
    handler: async (value) => {
      return await searchProducts(value)
    }
  }
}
```

### 3. 表单验证优化

```typescript
// 异步验证优化
{
  key: 'username',
  label: '用户名',
  type: 'text',
  form: {
    rules: [
      {
        validator: async (value) => {
          // 使用缓存避免重复请求
          const exists = await checkUsernameExists(value, { cache: true })
          return exists ? '用户名已存在' : true
        },
        // 验证防抖
        debounce: 500
      }
    ]
  }
}
```

## 🐛 故障排除

### 常见问题

#### 1. 表格数据不显示

**问题**: 配置了字段但表格中没有数据显示

**解决方案**:
```typescript
// 检查字段配置
{
  key: 'name',
  label: '姓名',
  type: 'text',
  // 确保 table.show 为 true
  table: { show: true }
}

// 检查 API 响应格式
const response = {
  data: [...], // 数据数组
  pagination: {
    current_page: 1,
    per_page: 20,
    total: 100,
    total_pages: 5
  }
}
```

#### 2. 表单验证不生效

**问题**: 设置了验证规则但不生效

**解决方案**:
```typescript
// 确保在 form 配置中设置 rules
{
  key: 'email',
  label: '邮箱',
  type: 'email',
  form: {
    show: true,
    required: true,
    rules: [
      { required: true, message: '邮箱必填' },
      { type: 'email', message: '邮箱格式不正确' }
    ]
  }
}
```

#### 3. 字段联动不工作

**问题**: 配置了联动但没有效果

**解决方案**:
```typescript
// 检查联动配置
{
  key: 'city',
  label: '城市',
  type: 'select',
  linkage: {
    // 确保 trigger 字段名正确
    trigger: 'province',
    action: 'updateOptions',
    // 确保 handler 返回正确格式
    handler: async (value) => {
      const cities = await getCities(value)
      return cities.map(city => ({
        label: city.name,
        value: city.id
      }))
    }
  }
}
```

#### 4. 自定义插槽不显示

**问题**: 使用了插槽但内容不显示

**解决方案**:
```vue
<!-- 确保插槽名称正确 -->
<template #cell-status="{ value, row }">
  <Badge>{{ value }}</Badge>
</template>

<!-- 检查字段 key 是否匹配 -->
<!-- 插槽名格式: cell-{fieldKey} -->
```

### 调试技巧

#### 1. 开启调试模式

```typescript
const config: CurdConfig = {
  // 开启调试模式
  debug: true,
  // 其他配置...
}
```

#### 2. 使用钩子函数调试

```typescript
const config: CurdConfig = {
  hooks: {
    onError: (error, operation, context) => {
      console.error('CURD Error:', {
        error,
        operation,
        context
      })
    },
    beforeList: (params) => {
      console.log('Before list:', params)
      return params
    },
    afterList: (response) => {
      console.log('After list:', response)
    }
  }
}
```

#### 3. 检查网络请求

```typescript
// 在浏览器开发者工具中检查网络请求
// 确保 API 响应格式正确
const api = createCosyApi({
  resource: 'users',
  // 添加请求拦截器进行调试
  axios: axios.create({
    baseURL: '/api',
    timeout: 10000
  })
})
```

## 🔧 组件配置

### CurdConfig 配置接口

```typescript
interface CurdConfig<T = any> {
  /** 页面标题 */
  title: string
  /** API 接口 */
  api: CurdApi<T>
  /** 字段配置数组（核心驱动） */
  fields: CurdFieldConfig<T>[]

  /** 操作按钮配置 */
  actions?: ActionsConfig | boolean
  /** 是否启用分页 */
  pagination?: boolean
  /** 每页条数 */
  pageSize?: number
  /** 每页条数选项 */
  pageSizeOptions?: number[]

  /** 主键字段名 */
  primaryKey?: string
  /** 按钮文本配置 */
  btnText?: ButtonText

  /** 消息配置 */
  confirmMessages?: {
    delete?: string
    batchDelete?: string
  }

  successMessages?: {
    create?: string
    update?: string
    delete?: string
    batchDelete?: string
  }

  /** hooks 钩子函数 */
  hooks?: CurdHooks<T>

  /** 权限配置 */
  permissions?: {
    create?: string
    edit?: string
    delete?: string
    batchDelete?: string
    export?: string
    import?: string
  }
}
```

### CurdFieldConfig 字段配置

```typescript
interface CurdFieldConfig<T = any> {
  /** 字段键名，对应接口字段 */
  key: keyof T
  /** 字段中文名，用于显示 */
  label: string
  /** 渲染类型 */
  type: FormFieldType
  /** 下拉等类型的选项配置 */
  options?: ReadonlyArray<FieldOption>
  /** 占位符 */
  placeholder?: string

  /** 表格展示相关配置 */
  table?: TableFieldConfig | boolean
  /** 表单展示相关配置 */
  form?: CurdFormFieldConfig | boolean
  /** 搜索相关配置 */
  search?: SearchFieldConfig | boolean

  /** 权限标识 */
  permission?: string
  /** 字段描述/帮助文本 */
  description?: string
  /** 字段分组 */
  group?: string
}
```

### 字段配置说明

每个字段支持的属性：

| 属性 | 说明 | 类型 |
|------|------|------|
| `key` | 字段键名，对应接口字段 | `keyof T` |
| `label` | 字段中文名，用于显示 | `string` |
| `type` | 渲染类型（text、select、switch、datetime等） | `FormFieldType` |
| `options` | 下拉等类型的选项配置 | `FieldOption[]` |
| `table` | 表格展示相关配置 | `TableFieldConfig \| boolean` |
| `form` | 表单展示相关配置 | `CurdFormFieldConfig \| boolean` |
| `search` | 搜索相关配置 | `SearchFieldConfig \| boolean` |

### 表格字段配置

```typescript
interface TableFieldConfig {
  /** 是否在表格中显示 */
  show?: boolean
  /** 自定义插槽名称 */
  slot?: string
  /** 列宽 */
  width?: number | string
  /** 是否可排序 */
  sortable?: boolean
  /** 是否固定列 */
  fixed?: 'left' | 'right'
  /** 自定义渲染函数 */
  render?: (value: any, record: any) => any
}
```

### 表单字段配置

```typescript
interface CurdFormFieldConfig {
  /** 是否在表单中显示 */
  show?: boolean | ((formData: Record<string, any>) => boolean)
  /** 是否必填 */
  required?: boolean
  /** 是否禁用 */
  disabled?: boolean | ((formData: Record<string, any>) => boolean)
  /** 默认值 */
  defaultValue?: any
  /** 验证规则 */
  rules?: ValidationRule[]
  /** 列宽配置（1-12） */
  col?: number
  /** 占位符 */
  placeholder?: string
  /** 组件特定属性 */
  componentProps?: Record<string, any>
}
```

### 搜索字段配置

```typescript
interface SearchFieldConfig {
  /** 是否支持搜索 */
  show?: boolean
  /** 搜索占位符 */
  placeholder?: string
  /** 搜索类型 */
  searchType?: 'exact' | 'like' | 'range'
  /** 组件特定属性 */
  componentProps?: Record<string, any>
}
```

## 💡 使用提示

### 1. 字段命名规范

```typescript
// 推荐使用下划线命名
{
  key: 'user_name', // ✅ 推荐
  key: 'userName',  // ❌ 不推荐
}

// 插槽名会自动转换为 kebab-case
// user_name -> #cell-user-name
```

### 2. 类型安全最佳实践

```typescript
// 定义完整的数据类型
interface User {
  id: number
  user_name: string
  email: string
  created_at: string
}

// 使用泛型确保类型安全
const config: CurdConfig<User> = {
  // TypeScript 会提供完整的类型提示
  fields: [
    {
      key: 'user_name', // 只能选择 User 接口中的字段
      // ...
    }
  ]
}
```

### 3. 性能优化建议

```typescript
// 对于大量数据，使用分页
const config: CurdConfig = {
  pageSize: 20, // 适中的分页大小
  pageSizeOptions: [10, 20, 50, 100],

  // 只显示必要的字段
  fields: [
    {
      key: 'description',
      table: { show: false }, // 长文本字段不在表格中显示
      form: { show: true }
    }
  ]
}
```

### 4. 错误处理建议

```typescript
const config: CurdConfig = {
  hooks: {
    onError: async (error, operation, context) => {
      // 统一错误处理
      if (error.response?.status === 401) {
        // 处理认证错误
        router.push('/login')
      } else if (error.response?.status === 403) {
        // 处理权限错误
        toast.error('没有操作权限')
      } else {
        // 其他错误
        toast.error(error.message || '操作失败')
      }
    }
  }
}
```

## ⚠️ 注意事项

### 1. API 接口规范

确保后端 API 遵循以下响应格式：

```json
// 列表接口响应
{
  "data": [...],
  "pagination": {
    "current_page": 1,
    "per_page": 20,
    "total": 100,
    "total_pages": 5
  }
}

// 单个资源接口响应
{
  "id": 1,
  "name": "用户名",
  // ... 其他字段
}
```

### 2. 字段类型匹配

确保字段类型与实际数据类型匹配：

```typescript
{
  key: 'created_at',
  type: 'datetime', // 对应 ISO 8601 格式的日期字符串
  // 不要用于 Unix 时间戳
}

{
  key: 'price',
  type: 'number', // 对应数字类型
  // 不要用于字符串格式的数字
}
```

### 3. 插槽使用限制

- 插槽名必须与字段 key 对应
- 自定义插槽会覆盖默认渲染
- 注意插槽参数的类型安全

### 4. 浏览器兼容性

- 支持现代浏览器 (Chrome 90+, Firefox 88+, Safari 14+)
- 不支持 IE 浏览器
- 移动端需要适配触摸操作

## 🤝 贡献指南

欢迎贡献代码！请遵循以下步骤：

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

### 代码规范

- 使用 TypeScript 编写代码
- 遵循 ESLint 和 Prettier 配置
- 为新功能添加测试用例
- 更新相关文档
- 提交信息遵循 [Conventional Commits](https://conventionalcommits.org/) 规范

## 📄 许可证

MIT License

## 🔗 相关链接

- [更新日志](./CHANGELOG.md)
- [迁移指南](./MIGRATION_GUIDE.md)
- [贡献指南](./CONTRIBUTING.md)
- [问题反馈](https://github.com/billing-sys/billing-web/issues)

## 📊 项目状态

![npm version](https://img.shields.io/npm/v/@billing/curd)
![npm downloads](https://img.shields.io/npm/dm/@billing/curd)
![license](https://img.shields.io/npm/l/@billing/curd)
![TypeScript](https://img.shields.io/badge/TypeScript-Ready-blue)
![Vue 3](https://img.shields.io/badge/Vue-3.x-green)

---

**开始构建你的第一个现代化 CURD 系统吧！🎉**

如果这个项目对你有帮助，请给我们一个 ⭐️ Star！

## 🙏 致谢

感谢以下开源项目的支持：

- [Vue 3](https://vuejs.org/) - 渐进式 JavaScript 框架
- [TanStack Table](https://tanstack.com/table) - 强大的表格解决方案
- [shadcn-vue](https://www.shadcn-vue.com/) - 现代化 UI 组件库
- [TypeScript](https://www.typescriptlang.org/) - JavaScript 的超集
- [Vite](https://vitejs.dev/) - 下一代前端构建工具
