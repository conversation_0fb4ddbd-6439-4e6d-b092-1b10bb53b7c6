要设计一个 **Vue 一键 CURD 表格组件** 的配置项，目标是**提升灵活性、降低学习成本、增强扩展性**。你可以参考以下思路来设计配置项结构，结合你用的 Vue 版本（Vue 3）和组件库（如 Ant Design Vue 或 shadcn-vue），这里我提供一种通用的设计方案结构：

---

### 💡 核心思路

* 提供 **统一规范的配置项结构**，减少组件调用复杂度
* **字段配置（fields）为核心驱动**，支持灵活类型渲染、校验、搜索、表单、权限等功能
* 提供默认行为，**只需要配置必要字段即可快速起步**
* 支持插槽 / 自定义渲染函数 / 自定义 API Hook，提高可扩展性

---

### 🧱 基础结构设计（简洁模式）

```ts
const crudConfig = {
  title: '用户管理',
  api: CurdApi,
  fields: [
    {
      key: 'id',
      label: 'ID',
      type: 'text',
      table: { show: true },
      form: { show: false },
      search: { show: false }
    },
    {
      key: 'username',
      label: '用户名',
      type: 'input',
      table: { show: true },
      form: { required: true },
      search: { placeholder: '请输入用户名' }
    },
    {
      key: 'role',
      label: '角色',
      type: 'select',
      options: [
        { label: '管理员', value: 'admin' },
        { label: '用户', value: 'user' }
      ],
      table: { show: true },
      form: { required: true },
      search: true
    },
    {
      key: 'created_at',
      label: '创建时间',
      type: 'datetime',
      table: { show: true },
      search: {
        type: 'date-range'
      },
      form: { show: false }
    }
  ],
  actions: {
    add: true,
    edit: true,
    delete: true
  },
  pagination: true
}
```

---

### 📐 字段配置说明（fields）

每个字段支持的属性可以统一：

| 属性名       | 说明                                                                     |
| --------- | ---------------------------------------------------------------------- |
| `key`     | 字段键名，对应接口字段                                                            |
| `label`   | 字段中文名，用于显示                                                             |
| `type`    | 渲染类型（input、select、switch、datetime、custom）                              |
| `options` | 下拉等类型的选项配置                                                             |
| `table`   | 表格展示相关配置 `{ show: boolean, slot?: string }`                            |
| `form`    | 表单展示相关配置 `{ show: boolean, required?: boolean, default?: any }`        |
| `search`  | 是否支持搜索，或者搜索配置 `{ show: boolean, type?: string, placeholder?: string }` |

---

### 🛠️ 进阶支持（可选）

* **权限控制**：字段、操作按钮都可以加权限标识（如 `permission: 'user.edit'`）
* **字段自定义渲染**：

  * 支持 `slot` / `render` 函数
* **自定义校验**：form 中可以提供 `rules` 来传递校验规则
* **联动字段**：可支持 `onChange` 或者字段依赖（如地址级联）

```ts
{
  key: 'status',
  label: '状态',
  type: 'switch',
  form: {
    default: true,
    onChange: (value, formModel) => {
      if (!value) formModel.reason = ''
    }
  }
}
```

---

### 🧩 插槽设计（可选）

支持自定义渲染插槽（用于复杂内容，如图片预览、操作按钮）：

```vue
<template #table-column-role="{ record }">
  <Tag :color="record.role === 'admin' ? 'blue' : 'green'">
    {{ record.role }}
  </Tag>
</template>
```

字段配置：

```ts
{
  key: 'role',
  label: '角色',
  type: 'custom',
  table: { slot: 'table-column-role' }
}
```

---

### 📚 降低学习成本建议

1. **提供丰富的预设字段类型**：input、textarea、select、switch、datetime、upload...
2. **提供表单、搜索、表格的默认配置**：如不传 `table.show` 默认都展示
3. **字段配置即约定即配置**，写字段即可自动生成增删改查行为

---

### ✅ 示例最终用法

```vue
<StdCurd :config="crudConfig" />
```

---

### 🧭 总结设计要点

| 目标     | 实现策略                       |
| ------ | -------------------------- |
| 快速上手   | 字段驱动，一份配置搞定增删改查            |
| 灵活性强   | 支持 slot/render、自定义请求、自定义布局 |
| 易于维护   | 字段配置统一规范                   |
| 降低认知负担 | 默认配置合理，文档示例清晰              |
| 支持权限   | 按钮/字段/操作权限集中配置             |

---

如果你想进一步做成一个 **npm包+组件库模式**，可以将这个库拆成：

* `@crud/core`：负责类型定义、字段解析、表单生成逻辑
* `@crud/antdv`：UI 绑定 Ant Design Vue
* `@crud/shadcn`：UI 绑定 shadcn-vue
* `@crud/devtools`：字段配置可视化生成器
