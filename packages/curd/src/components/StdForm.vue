<script setup lang="ts">
import type { CurdFormFieldConfig } from '../types'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@billing/ui'
import { onMounted, onUnmounted, reactive, ref, watch } from 'vue'
import { useCurdFormValidation } from '../composables/useCurdFormValidation'
import { useFieldLinkage } from '../composables/useFieldLinkage'
import CurdFormField from './StdFormField.vue'

interface Props {
  open: boolean
  title: string
  fields: CurdFormFieldConfig[]
  initialData?: Record<string, any>
  loading?: boolean
  mode?: 'create' | 'edit'
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'create',
  loading: false,
})

const emit = defineEmits<{
  'update:open': [value: boolean]
  'submit': [data: Record<string, any>]
  'cancel': []
}>()

// 表单数据
const formData = reactive<Record<string, any>>({})

// 用于存储字段的旧值，以便在变化时进行比较
const previousValues = ref<Record<string, any>>({})

// 使用验证组合函数
const { errors, validateForm, clearError, getError } = useCurdFormValidation()

// 使用字段联动组合函数
const {
  fieldStates,
  onFieldChange,
  initializeLinkage,
  getFieldOptions,
  getFieldState,
  getFieldDynamicProps,
  cleanup,
} = useFieldLinkage(props.fields, formData)

// 初始化表单数据
function initFormData() {
  const data: Record<string, any> = {}

  props.fields.forEach((field) => {
    const key = field.key as string

    if (props.initialData && props.initialData[key] !== undefined) {
      data[key] = props.initialData[key]
    }
    else if (field.default !== undefined) {
      data[key] = field.default
    }
    else {
      // 根据字段类型设置默认值
      switch (field.type) {
        case 'checkbox':
        case 'switch':
          data[key] = false
          break
        case 'number':
          data[key] = 0
          break
        case 'multiselect':
          data[key] = []
          break
        default:
          data[key] = undefined
      }
    }
  })

  Object.assign(formData, data)

  // 初始化旧值记录
  previousValues.value = { ...data }
}

// 生命周期钩子
onMounted(async () => {
  if (props.open) {
    initFormData()
    await initializeLinkage()
  }
})

onUnmounted(() => {
  cleanup()
})

// 监听初始数据变化
watch(() => props.initialData, (newData) => {
  if (newData) {
    Object.assign(formData, newData)
  }
}, { immediate: true })

// 监听打开状态
watch(() => props.open, async (isOpen) => {
  console.log(props.open)
  if (isOpen) {
    initFormData()
    await initializeLinkage()
  }
})

// 提交表单
function handleSubmit() {
  if (validateForm(props.fields, formData)) {
    emit('submit', { ...formData })
  }
}

// 取消
function handleCancel() {
  emit('cancel')
  emit('update:open', false)
}

// 关闭对话框
function handleClose() {
  emit('update:open', false)
}

// 判断字段是否显示
function isFieldVisible(field: CurdFormFieldConfig) {
  if (typeof field.show === 'function') {
    return field.show(formData)
  }
  return field.show !== false
}

// 判断字段是否禁用
function isFieldDisabled(field: CurdFormFieldConfig) {
  if (typeof field.disabled === 'function') {
    return field.disabled(formData)
  }
  return field.disabled === true
}

// 处理字段值变化
async function handleFieldChange(field: CurdFormFieldConfig, value: any) {
  const fieldKey = field.key as string
  const oldValue = previousValues.value[fieldKey]

  // 更新表单数据
  formData[fieldKey] = value

  // 更新旧值记录
  previousValues.value[fieldKey] = value

  // 清除该字段的错误
  clearError(fieldKey)

  // 处理字段联动
  await onFieldChange(fieldKey, value, oldValue)
}
</script>

<template>
  <Dialog
    :open="open"
    @update:open="handleClose"
  >
    <DialogContent class="max-w-2xl max-h-[80vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle>{{ title }}</DialogTitle>
      </DialogHeader>

      <div class="grid grid-cols-1 gap-4">
        <template
          v-for="field in fields"
          :key="field.key as string"
        >
          <div
            v-if="isFieldVisible(field)"
            :class="field.col ? `col-span-${field.col}` : 'col-span-1'"
          >
            <CurdFormField
              :field="field"
              :model-value="formData[field.key as string]"
              :disabled="isFieldDisabled(field)"
              :error="getError(field.key as string)"
              :field-options="getFieldOptions(field.key as string)"
              :field-state="getFieldState(field.key as string)"
              :dynamic-props="getFieldDynamicProps(field.key as string)"
              @update:model-value="(value) => handleFieldChange(field, value)"
            />
          </div>
        </template>
      </div>

      <DialogFooter>
        <Button
          variant="outline"
          @click="handleCancel"
        >
          取消
        </Button>
        <Button
          :loading="loading"
          @click="handleSubmit"
        >
          {{ mode === 'create' ? '创建' : '更新' }}
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
