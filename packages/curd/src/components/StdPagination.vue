<script setup lang="ts">
import type { AcceptableValue } from 'reka-ui'
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationFirst,
  PaginationItem,
  PaginationLast,
  PaginationNext,
  PaginationPrevious,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@billing/ui'
import { computed } from 'vue'

interface Props {
  currentPage: number
  pageSize: number
  total: number
  pageSizes?: number[]
}

const props = withDefaults(defineProps<Props>(), {
  pageSizes: () => [10, 20, 50, 100],
})

const emit = defineEmits<{
  'update:currentPage': [page: number]
  'update:pageSize': [size: number]
}>()

// 总页数
const totalPages = computed(() => Math.ceil(props.total / props.pageSize))

// 生成页码数组
const paginationPages = computed(() => {
  const pages: Array<{ type: 'page' | 'ellipsis', value?: number }> = []
  const current = props.currentPage
  const total = totalPages.value

  if (total <= 7) {
    // 总页数小于等于7，显示所有页码
    for (let i = 1; i <= total; i++) {
      pages.push({ type: 'page', value: i })
    }
  }
  else {
    // 总页数大于7，需要显示省略号
    if (current <= 3) {
      // 当前页靠近开头
      for (let i = 1; i <= 5; i++) {
        pages.push({ type: 'page', value: i })
      }
      pages.push({ type: 'ellipsis' })
      pages.push({ type: 'page', value: total })
    }
    else if (current >= total - 2) {
      // 当前页靠近结尾
      pages.push({ type: 'page', value: 1 })
      pages.push({ type: 'ellipsis' })
      for (let i = total - 4; i <= total; i++) {
        pages.push({ type: 'page', value: i })
      }
    }
    else {
      // 当前页在中间
      pages.push({ type: 'page', value: 1 })
      pages.push({ type: 'ellipsis' })
      for (let i = current - 1; i <= current + 1; i++) {
        pages.push({ type: 'page', value: i })
      }
      pages.push({ type: 'ellipsis' })
      pages.push({ type: 'page', value: total })
    }
  }

  return pages
})

// 处理页码变化
function handlePageChange(page: number) {
  if (page >= 1 && page <= totalPages.value) {
    emit('update:currentPage', page)
  }
}

// 处理每页条数变化
function handlePageSizeChange(value: AcceptableValue) {
  emit('update:pageSize', Number(value))
}
</script>

<template>
  <div class="curd-pagination">
    <Pagination
      class="justify-end"
      :total="total"
      :items-per-page="pageSize"
      :sibling-count="1"
      show-edges
      :page="currentPage"
      @update:page="handlePageChange"
    >
      <PaginationContent>
        <div class="flex items-center gap-2">
          <span class="text-sm text-muted-foreground">
            共 {{ total }} 条
          </span>

          <Select
            :model-value="String(pageSize)"
            @update:model-value="handlePageSizeChange"
          >
            <SelectTrigger class="w-fit h-8">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem
                v-for="size in pageSizes"
                :key="size"
                :value="String(size)"
              >
                {{ size }} 条/页
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        <PaginationFirst />
        <PaginationPrevious />

        <template
          v-for="(item, index) in paginationPages"
          :key="index"
        >
          <PaginationItem
            v-if="item.type === 'page'"
            :value="item.value || 1"
            :is-active="item.value === currentPage"
          >
            {{ item.value }}
          </PaginationItem>
          <PaginationEllipsis
            v-else-if="item.type === 'ellipsis'"
            :index="index"
          />
        </template>

        <PaginationNext />
        <PaginationLast />
      </PaginationContent>
    </Pagination>
  </div>
</template>

<style scoped>
.curd-pagination {
  @apply w-full flex items-center justify-end;
}
</style>
