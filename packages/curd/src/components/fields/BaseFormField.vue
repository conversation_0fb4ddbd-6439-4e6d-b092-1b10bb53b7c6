<script setup lang="ts">
import type { CurdFormFieldConfig } from '../../types'
import { Label } from '@billing/ui'
import { useForm<PERSON>ield } from '../../composables/useFormField'

interface Props {
  field: CurdFormFieldConfig
  modelValue: any
  disabled?: boolean
  error?: string
  showLabel?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  showLabel: true,
})

const emit = defineEmits<{
  'update:modelValue': [value: any]
}>()

const { showRequired, isVisible } = useFormField(props)

// 处理值变化
function handleChange(value: any) {
  emit('update:modelValue', value)
}
</script>

<template>
  <div
    v-if="isVisible"
    class="space-y-2"
  >
    <!-- 标签（除了特殊组件外都需要） -->
    <Label
      v-if="showLabel && !['checkbox', 'switch'].includes(field.type || '')"
      :for="field.key as string"
    >
      {{ field.label }}
      <span
        v-if="showRequired"
        class="text-destructive"
      >*</span>
    </Label>

    <!-- 字段内容插槽 -->
    <slot
      :field="field"
      :model-value="modelValue"
      :disabled="disabled"
      :handle-change="handleChange"
    />

    <!-- 错误信息 -->
    <div
      v-if="error"
      class="text-sm text-destructive"
    >
      {{ error }}
    </div>
  </div>
</template>
