<script setup lang="ts">
import type { CurdFormFieldConfig } from '../../types'
import {
  Combobox,
  ComboboxAnchor,
  ComboboxEmpty,
  ComboboxGroup,
  ComboboxItem,
  ComboboxItemIndicator,
  ComboboxList,
  ComboboxTrigger,
  Input,
} from '@billing/ui'
import { Check } from 'lucide-vue-next'
import { reactive, watch } from 'vue'

interface FieldState {
  loading: boolean
  error: string | null
  dynamicOptions: { label: string, value: any, [key: string]: any }[]
  dynamicProps: Record<string, any>
}

interface Props {
  field: CurdFormFieldConfig
  modelValue: any
  disabled?: boolean
  error?: string
  fieldOptions?: { label: string, value: any, [key: string]: any }[]
  fieldState?: FieldState
  dynamicProps?: Record<string, any>
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  fieldOptions: () => [],
  fieldState: () => ({
    loading: false,
    error: null,
    dynamicOptions: [],
    dynamicProps: {},
  }),
  dynamicProps: () => ({}),
})

const emit = defineEmits<{
  'update:modelValue': [value: any]
}>()

// 获取初始选项（优先使用动态选项）
function getInitialOptions() {
  // 如果有联动传入的选项，优先使用
  if (props.fieldOptions && props.fieldOptions.length > 0) {
    return props.fieldOptions
  }

  // 如果有字段状态中的动态选项，使用它
  if (props.fieldState?.dynamicOptions?.length > 0) {
    return props.fieldState.dynamicOptions
  }

  // 使用远程搜索的默认选项或静态选项
  return props.field.componentProps?.remoteSearch?.defaultOptions || props.field.componentProps?.options || []
}

// Combobox 状态
const state = reactive({
  open: false,
  searchQuery: '',
  options: getInitialOptions(),
  loading: props.fieldState?.loading || false,
  selectedValue: props.modelValue || null,
})

// 防抖定时器
let debounceTimer: number | null = null

// 初始化状态
function initState() {
  state.open = false
  state.searchQuery = ''
  state.options = props.field.componentProps?.remoteSearch?.defaultOptions || props.field.componentProps?.options || []
  state.loading = false
  state.selectedValue = props.modelValue || null
}

// 处理搜索
async function handleSearch(query: string) {
  const config = props.field.componentProps?.remoteSearch

  if (!config) {
    // 如果没有远程搜索配置，使用本地搜索
    if (props.field.componentProps?.options) {
      state.options = props.field.componentProps.options.filter(option =>
        option.label.toLowerCase().includes(query.toLowerCase()),
      )
    }
    return
  }

  // 检查最小搜索长度
  if (query.length < (config.minSearchLength || 1)) {
    if (config.showDefaultOptions && config.defaultOptions) {
      state.options = config.defaultOptions
    }
    else {
      state.options = []
    }
    return
  }

  // 清除之前的防抖定时器
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }

  // 设置新的防抖定时器
  debounceTimer = setTimeout(async () => {
    try {
      state.loading = true
      const results = await config.searchApi(query)
      state.options = results
    }
    catch (error) {
      console.error(`Combobox search error for field ${props.field.key as string}:`, error)
      state.options = []
    }
    finally {
      state.loading = false
    }
  }, config.debounceMs || 300)
}

// 处理值变化
function handleValueChange(value: any) {
  state.selectedValue = value
  emit('update:modelValue', value)
}

// 获取显示文本
function getDisplayText(value: any): string {
  if (!value)
    return ''

  const option = state.options.find(opt => opt.value === value)
  if (option) {
    if (props.field.componentProps?.remoteSearch?.renderOption) {
      return props.field.componentProps.remoteSearch.renderOption(option)
    }
    return option.label
  }

  return state.searchQuery
}

// 处理输入聚焦
function handleFocus() {
  if (!state.searchQuery && props.field.componentProps?.remoteSearch?.showDefaultOptions) {
    handleSearch('')
  }
}

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  state.selectedValue = newValue
})

// 监听字段配置变化
watch(() => props.field, () => {
  initState()
}, { immediate: true })

// 监听动态选项变化
watch(() => props.fieldOptions, (newOptions) => {
  if (newOptions && newOptions.length > 0) {
    state.options = newOptions
  }
}, { immediate: true })

// 监听字段状态变化
watch(() => props.fieldState, (newState) => {
  if (newState) {
    state.loading = newState.loading

    if (newState.dynamicOptions && newState.dynamicOptions.length > 0
      && (!props.fieldOptions || props.fieldOptions.length === 0)) {
      state.options = newState.dynamicOptions
    }
  }
}, { immediate: true, deep: true })
</script>

<template>
  <Combobox
    v-model:open="state.open"
    v-model="state.selectedValue"
    by="label"
    :use-portal="false"
    @update:model-value="handleValueChange"
  >
    <ComboboxAnchor>
      <ComboboxTrigger>
        <Input
          autocomplete="new-password"
          :placeholder="`请选择${field.label}`"
          :disabled="disabled"
          :model-value="getDisplayText(modelValue)"
          @update:model-value="(query) => {
            state.searchQuery = query as string
            handleSearch(query as string)
          }"
          @focus="handleFocus"
        />
      </ComboboxTrigger>
    </ComboboxAnchor>
    <ComboboxList>
      <ComboboxEmpty
        v-if="!state.loading && state.options.length === 0"
        class="text-sm text-muted-foreground py-4"
      >
        {{ state.searchQuery ? '未找到相关结果' : field.componentProps?.placeholder || '请输入关键词搜索' }}
      </ComboboxEmpty>

      <div
        v-if="state.loading"
        class="flex items-center justify-center py-2"
      >
        <div class="flex items-center gap-2 text-sm text-muted-foreground">
          <div class="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
          搜索中...
        </div>
      </div>

      <ComboboxGroup v-if="!state.loading && state.options.length">
        <ComboboxItem
          v-for="option in state.options"
          :key="option.value"
          :value="option.value"
          :disabled="option.disabled"
        >
          <ComboboxItemIndicator
            v-if="state.selectedValue === option.value"
            class="ml-0"
          >
            <Check class="w-4 h-4" />
          </ComboboxItemIndicator>
          <span>
            {{ field.componentProps?.remoteSearch?.renderOption ? field.componentProps.remoteSearch.renderOption(option) : option.label }}
          </span>
        </ComboboxItem>
      </ComboboxGroup>
    </ComboboxList>
  </Combobox>
  <div
    v-if="error"
    class="text-sm text-destructive"
  >
    {{ error }}
  </div>
</template>
