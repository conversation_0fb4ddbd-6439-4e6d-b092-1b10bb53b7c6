<script setup lang="ts">
import type { DateValue } from '@internationalized/date'
import type { CurdFormFieldConfig } from '../../types'
import {
  Button,
  Calendar,
  Input,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@billing/ui'
import {
  DateFormatter,
  fromDate,
  getLocalTimeZone,
  now,
  parseDate,
  parseDateTime,
  parseTime,
  toCalendarDate,
  toCalendarDateTime,
  today,
} from '@internationalized/date'
import { CalendarIcon, Clock } from 'lucide-vue-next'
import { computed, ref } from 'vue'
import { useFormField } from '../../composables/useFormField'
import BaseFormField from './BaseFormField.vue'

interface Props {
  field: CurdFormFieldConfig
  modelValue?: number | DateValue | null
  disabled?: boolean
  error?: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: number]
}>()

const df = new DateFormatter('zh-CN', {
  dateStyle: 'long',
})

const tf = new DateFormatter('zh-CN', {
  timeStyle: 'short',
})

const { isDisabled, getDefaultPlaceholder } = useFormField(props)

// 日期选择器状态
const datePickerOpen = ref(false)

// 将 timestamp 转换为 DateValue
function timestampToDateValue(timestamp: number): DateValue | null {
  if (!timestamp || timestamp === 0)
    return null

  try {
    // 检查是否需要转换时间戳格式（秒转毫秒）
    const timestampConfig = props.field.componentProps?.timestampConfig
    let actualTimestamp = timestamp

    if (timestampConfig?.format === 'seconds') {
      // 如果配置为秒，转换为毫秒
      actualTimestamp = timestamp * 1000
    }

    const date = new Date(actualTimestamp)
    return fromDate(date, getLocalTimeZone())
  }
  catch (error) {
    console.warn('Invalid timestamp:', timestamp, error)
    return null
  }
}

// 获取当前的 DateValue（支持 timestamp 和 DateValue）
const currentDateValue = computed((): DateValue | null => {
  if (!props.modelValue)
    return null

  // 如果是数字类型（timestamp）
  if (typeof props.modelValue === 'number') {
    return timestampToDateValue(props.modelValue)
  }

  // 如果是 DateValue 类型
  return props.modelValue
})

// 处理值变化
function handleChange(value: DateValue | null) {
  if (value) {
    const timestamp = value.toDate(getLocalTimeZone()).getTime()
    const timestampConfig = props.field.componentProps?.timestampConfig

    // 根据配置返回秒或毫秒
    if (timestampConfig?.format === 'seconds') {
      emit('update:modelValue', Math.floor(timestamp / 1000))
    }
    else {
      emit('update:modelValue', timestamp)
    }
  }
  else {
    emit('update:modelValue', 0)
  }
}

// 格式化日期显示
function formatDate(date: DateValue): string {
  if (!date)
    return ''

  switch (props.field.type) {
    case 'date':
      return df.format(date.toDate(getLocalTimeZone()))
    case 'datetime':
    case 'timestamp':
      return `${df.format(date.toDate(getLocalTimeZone()))} ${tf.format(date.toDate(getLocalTimeZone()))}`
    case 'time':
      return tf.format(date.toDate(getLocalTimeZone()))
    default:
      return df.format(date.toDate(getLocalTimeZone()))
  }
}

// 获取输入框类型
const inputType = computed(() => {
  switch (props.field.type) {
    case 'date':
      return 'date'
    case 'datetime':
    case 'timestamp':
      return 'datetime-local'
    case 'time':
      return 'time'
    default:
      return 'date'
  }
})

// 获取显示图标
const displayIcon = computed(() => {
  return props.field.type === 'time' ? Clock : CalendarIcon
})

// 处理日期选择器值变化
function handleCalendarChange(date: DateValue | undefined) {
  if (!date) {
    handleChange(null)
    return
  }

  if (props.field.type === 'time') {
    // 对于时间类型，保持当前日期，只更新时间
    const currentDate = currentDateValue.value ? toCalendarDate(currentDateValue.value) : today(getLocalTimeZone())
    const timeValue = 'hour' in date ? date : now(getLocalTimeZone())
    const combinedDateTime = toCalendarDateTime(currentDate, timeValue)
    handleChange(combinedDateTime)
  }
  else {
    handleChange(date)
  }
  datePickerOpen.value = false
}

// 处理原生输入框变化
function handleNativeInputChange(value: string) {
  if (!value) {
    handleChange(null)
    return
  }

  try {
    let dateValue: DateValue

    switch (props.field.type) {
      case 'date':
        dateValue = parseDate(value)
        break
      case 'datetime':
      case 'timestamp':
        dateValue = parseDateTime(value)
        break
      case 'time': {
        // 对于时间类型，需要结合当前日期创建一个 CalendarDateTime
        const timeValue = parseTime(value)
        const currentDate = currentDateValue.value ? toCalendarDate(currentDateValue.value) : today(getLocalTimeZone())
        dateValue = toCalendarDateTime(currentDate, timeValue)
        break
      }
      default:
        dateValue = parseDate(value)
    }

    handleChange(dateValue)
  }
  catch (error) {
    console.warn('Invalid date format:', value, error)
    handleChange(null)
  }
}

// 获取原生输入框的值
function getNativeInputValue(): string {
  const dateValue = currentDateValue.value
  if (!dateValue)
    return ''

  try {
    const date = dateValue.toDate(getLocalTimeZone())

    switch (props.field.type) {
      case 'date':
        return date.toISOString().split('T')[0]
      case 'datetime':
      case 'timestamp':
        return date.toISOString().slice(0, 16)
      case 'time':
        return date.toTimeString().slice(0, 5)
      default:
        return date.toISOString().split('T')[0]
    }
  }
  catch (error) {
    console.warn('Error formatting date:', error)
    return ''
  }
}

// 获取时间值用于时间输入框
function getTimeValue(): string {
  const dateValue = currentDateValue.value
  if (!dateValue)
    return ''

  try {
    const date = dateValue.toDate(getLocalTimeZone())
    return date.toTimeString().slice(0, 5)
  }
  catch (error) {
    console.warn('Error getting time value:', error)
    return ''
  }
}

// 处理时间输入变化
function handleTimeInputChange(timeValue: string) {
  if (!timeValue || !currentDateValue.value)
    return

  try {
    const [hours, minutes] = timeValue.split(':').map(Number)
    const currentDate = toCalendarDate(currentDateValue.value)
    const newTime = parseTime(`${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`)
    const combinedDateTime = toCalendarDateTime(currentDate, newTime)
    handleChange(combinedDateTime)
  }
  catch (error) {
    console.warn('Error setting time:', error)
  }
}
</script>

<template>
  <BaseFormField
    :field="field"
    :model-value="modelValue"
    :disabled="disabled"
    :error="error"
    @update:model-value="handleChange"
  >
    <template #default>
      <div class="space-y-2">
        <!-- 日历选择器 (适用于 date、datetime 和 timestamp) -->
        <Popover
          v-if="field.type !== 'time'"
          v-model:open="datePickerOpen"
        >
          <PopoverTrigger as-child>
            <Button
              variant="outline"
              class="w-full justify-start text-left font-normal"
              :class="{
                'text-muted-foreground': !currentDateValue,
              }"
              :disabled="isDisabled"
            >
              <component
                :is="displayIcon"
                class="mr-2 h-4 w-4"
              />
              {{ currentDateValue ? formatDate(currentDateValue) : getDefaultPlaceholder(field) }}
            </Button>
          </PopoverTrigger>
          <PopoverContent
            class="w-auto p-0"
            align="start"
          >
            <Calendar
              :model-value="currentDateValue || undefined"
              @update:model-value="handleCalendarChange"
            />

            <!-- 时间选择器 (仅用于 datetime 和 timestamp) -->
            <div
              v-if="field.type === 'datetime' || field.type === 'timestamp'"
              class="p-3 border-t"
            >
              <div class="flex items-center gap-2">
                <Clock class="h-4 w-4" />
                <Input
                  type="time"
                  :value="getTimeValue()"
                  :disabled="isDisabled"
                  @input="(e) => handleTimeInputChange((e.target as HTMLInputElement).value)"
                />
              </div>
            </div>
          </PopoverContent>
        </Popover>

        <!-- 时间选择器 (仅用于 time 类型) -->
        <div
          v-if="field.type === 'time'"
          class="flex items-center gap-2"
        >
          <component
            :is="displayIcon"
            class="h-4 w-4 text-muted-foreground"
          />
          <Input
            :type="inputType"
            :value="getNativeInputValue()"
            :disabled="isDisabled"
            :placeholder="getDefaultPlaceholder(field)"
            @input="(e) => handleNativeInputChange((e.target as HTMLInputElement).value)"
          />
        </div>
      </div>
    </template>
  </BaseFormField>
</template>
