<script setup lang="ts">
import type { CurdFormFieldConfig } from '../../types'
import { Button } from '@billing/ui'
import { Upload, X } from 'lucide-vue-next'
import { ref } from 'vue'
import { useFormField } from '../../composables/useFormField'
import BaseFormField from './BaseFormField.vue'

interface Props {
  field: CurdFormFieldConfig
  modelValue: any
  disabled?: boolean
  error?: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: any]
}>()

const { isDisabled } = useFormField(props)
const fileInput = ref<HTMLInputElement>()

// 处理值变化
function handleChange(value: any) {
  emit('update:modelValue', value)
}

// 处理文件上传
function handleFileChange(event: Event) {
  const target = event.target as HTMLInputElement
  const files = target.files

  if (!files || files.length === 0)
    return

  const file = files[0]
  const config = props.field.componentProps?.fileConfig

  // 文件大小检查
  if (config?.maxSize && file.size > config.maxSize) {
    console.error(`文件大小超过限制: ${config.maxSize} 字节`)
    return
  }

  // 文件类型检查
  if (config?.accept && !isFileTypeAccepted(file, config.accept)) {
    console.error(`不支持的文件类型: ${file.type}`)
    return
  }

  // 如果有自定义上传函数
  if (config?.customUpload) {
    config.customUpload(file).then((result) => {
      handleChange(result.url)
    }).catch((error) => {
      console.error('文件上传失败:', error)
    })
  }
  else {
    // 创建预览 URL
    const url = URL.createObjectURL(file)
    handleChange(url)
  }
}

// 检查文件类型是否被接受
function isFileTypeAccepted(file: File, accept: string): boolean {
  const acceptTypes = accept.split(',').map(type => type.trim())
  return acceptTypes.some((type) => {
    if (type.includes('*')) {
      const mimeCategory = type.split('/')[0]
      return file.type.startsWith(mimeCategory)
    }
    return file.type === type || file.name.toLowerCase().endsWith(type)
  })
}

// 触发文件选择
function triggerFileSelect() {
  fileInput.value?.click()
}

// 删除文件
function removeFile() {
  handleChange(null)
}
</script>

<template>
  <BaseFormField
    :field="field"
    :model-value="modelValue"
    :disabled="disabled"
    :error="error"
    @update:model-value="handleChange"
  >
    <template #default>
      <div class="space-y-2">
        <input
          ref="fileInput"
          type="file"
          class="hidden"
          :accept="field.componentProps?.fileConfig?.accept"
          :multiple="field.type?.includes('multiple')"
          @change="handleFileChange"
        >

        <!-- 文件预览 -->
        <div
          v-if="modelValue"
          class="space-y-2"
        >
          <div
            v-if="field.type?.includes('image')"
            class="relative inline-block"
          >
            <img
              :src="modelValue"
              :alt="field.label"
              class="w-20 h-20 object-cover rounded border"
            >
            <Button
              variant="destructive"
              size="sm"
              class="absolute -top-2 -right-2 h-6 w-6 p-0"
              @click="removeFile"
            >
              <X class="h-3 w-3" />
            </Button>
          </div>
          <div
            v-else
            class="flex items-center gap-2 p-2 border rounded"
          >
            <span class="text-sm truncate">{{ modelValue }}</span>
            <Button
              variant="ghost"
              size="sm"
              @click="removeFile"
            >
              <X class="h-4 w-4" />
            </Button>
          </div>
        </div>

        <!-- 上传按钮 -->
        <Button
          v-if="!modelValue || field.type?.includes('multiple')"
          variant="outline"
          :disabled="isDisabled"
          @click="triggerFileSelect"
        >
          <Upload class="mr-2 h-4 w-4" />
          {{ field.type?.includes('image') ? '选择图片' : '选择文件' }}
        </Button>
      </div>
    </template>
  </BaseFormField>
</template>
