<script setup lang="ts">
import type { CurdFormFieldConfig } from '../../types'
import { Input, Textarea } from '@billing/ui'
import { useFormField } from '../../composables/useFormField'
import BaseFormField from './BaseFormField.vue'

interface Props {
  field: CurdFormFieldConfig
  modelValue: any
  disabled?: boolean
  error?: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: any]
}>()

const { isDisabled, getFieldProps } = useFormField(props)

// 处理值变化
function handleChange(value: any) {
  emit('update:modelValue', value)
}
</script>

<template>
  <BaseFormField
    :field="field"
    :model-value="modelValue"
    :disabled="disabled"
    :error="error"
    @update:model-value="handleChange"
  >
    <template #default="{ handleChange: onChange }">
      <!-- Textarea 多行文本 -->
      <Textarea
        v-if="field.type === 'textarea'"
        :model-value="modelValue"
        :disabled="isDisabled"
        v-bind="getFieldProps(field)"
        @update:model-value="onChange"
      />

      <!-- 基础输入框 -->
      <Input
        v-else
        :model-value="modelValue"
        :disabled="isDisabled"
        v-bind="getFieldProps(field)"
        @update:model-value="onChange"
      />
    </template>
  </BaseFormField>
</template>
