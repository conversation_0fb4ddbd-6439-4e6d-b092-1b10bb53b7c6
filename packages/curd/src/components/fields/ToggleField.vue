<script setup lang="ts">
import type { CurdFormFieldConfig } from '../../types'
import { Checkbox, Label, Switch } from '@billing/ui'
import { useFormField } from '../../composables/useFormField'
import BaseFormField from './BaseFormField.vue'

interface Props {
  field: CurdFormFieldConfig
  modelValue: any
  disabled?: boolean
  error?: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: any]
}>()

const { isDisabled } = useFormField(props)

// 处理值变化
function handleChange(value: any) {
  emit('update:modelValue', value)
}
</script>

<template>
  <BaseFormField
    :field="field"
    :model-value="modelValue"
    :disabled="disabled"
    :error="error"
    :show-label="false"
    @update:model-value="handleChange"
  >
    <template #default="{ handleChange: onChange }">
      <!-- Switch 开关 -->
      <div
        v-if="field.type === 'switch'"
        class="space-y-2"
      >
        <Label>{{ field.label }}</Label>
        <Switch
          :model-value="modelValue"
          :disabled="isDisabled"
          @update:model-value="onChange"
        />
      </div>

      <!-- Checkbox 复选框 -->
      <div
        v-else-if="field.type === 'checkbox'"
        class="space-y-2"
      >
        <Checkbox
          :model-value="modelValue"
          :disabled="isDisabled"
          @update:model-value="onChange"
        />
        <Label>{{ field.label }}</Label>
      </div>
    </template>
  </BaseFormField>
</template>
