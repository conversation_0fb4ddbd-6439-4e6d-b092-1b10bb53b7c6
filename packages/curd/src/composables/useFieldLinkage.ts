import type { CurdFormFieldConfig, FormFieldOptions } from '../types'
import { nextTick, reactive } from 'vue'

interface FieldState {
  loading: boolean
  error: string | null
  dynamicOptions: FormFieldOptions
  dynamicProps: Record<string, any>
}

interface LinkageState {
  fieldStates: Record<string, FieldState>
  dependencyMap: Record<string, string[]> // key: 字段key, value: 依赖此字段的其他字段
  watcherMap: Record<string, string[]> // key: 字段key, value: 监听此字段的其他字段
  debounceTimers: Record<string, number>
}

export function useFieldLinkage(
  fields: CurdFormFieldConfig[],
  formData: Record<string, any>,
) {
  // 联动状态
  const state = reactive<LinkageState>({
    fieldStates: {},
    dependencyMap: {},
    watcherMap: {},
    debounceTimers: {},
  })

  // 初始化字段状态
  function initFieldStates() {
    fields.forEach((field) => {
      const key = field.key as string

      state.fieldStates[key] = {
        loading: false,
        error: null,
        dynamicOptions: field.componentProps?.options || [],
        dynamicProps: {},
      }

      // 构建依赖映射
      if (field.linkage?.dependsOn) {
        field.linkage.dependsOn.forEach((depField) => {
          if (!state.dependencyMap[depField]) {
            state.dependencyMap[depField] = []
          }
          state.dependencyMap[depField].push(key)
        })
      }

      // 构建监听映射
      if (field.watcher?.watchFields) {
        field.watcher.watchFields.forEach((watchField) => {
          if (!state.watcherMap[watchField]) {
            state.watcherMap[watchField] = []
          }
          state.watcherMap[watchField].push(key)
        })
      }
    })
  }

  // 获取字段配置
  function getFieldConfig(fieldKey: string): CurdFormFieldConfig | undefined {
    return fields.find(field => field.key === fieldKey)
  }

  // 获取依赖字段的值
  function getDependentValues(dependsOn: string[]): Record<string, any> {
    const values: Record<string, any> = {}
    dependsOn.forEach((key) => {
      values[key] = formData[key]
    })
    return values
  }

  // 处理字段联动
  async function handleFieldLinkage(fieldKey: string) {
    const fieldConfig = getFieldConfig(fieldKey)
    if (!fieldConfig?.linkage)
      return

    const { dependsOn, handler, debounceMs = 300, clearOnDependentChange = true } = fieldConfig.linkage

    // 清除之前的防抖定时器
    if (state.debounceTimers[fieldKey]) {
      clearTimeout(state.debounceTimers[fieldKey])
    }

    // 设置新的防抖定时器
    state.debounceTimers[fieldKey] = setTimeout(async () => {
      try {
        state.fieldStates[fieldKey].loading = true
        state.fieldStates[fieldKey].error = null

        // 如果配置了清空值，则清空当前字段
        if (clearOnDependentChange) {
          formData[fieldKey] = getDefaultValue(fieldConfig)
        }

        const dependentValues = getDependentValues(dependsOn)
        const newOptions = await handler(dependentValues, formData)

        state.fieldStates[fieldKey].dynamicOptions = Array.isArray(newOptions) ? newOptions : []
      }
      catch (error) {
        console.error(`字段联动处理失败 [${fieldKey}]:`, error)
        state.fieldStates[fieldKey].error = fieldConfig.linkage?.errorMessage || '加载选项失败'
        state.fieldStates[fieldKey].dynamicOptions = []
      }
      finally {
        state.fieldStates[fieldKey].loading = false
      }
    }, debounceMs)
  }

  // 处理动态选项
  async function handleDynamicOptions(fieldKey: string) {
    const fieldConfig = getFieldConfig(fieldKey)
    if (!fieldConfig?.dynamicOptions)
      return

    try {
      state.fieldStates[fieldKey].loading = true
      const newOptions = await fieldConfig.dynamicOptions(formData)
      state.fieldStates[fieldKey].dynamicOptions = Array.isArray(newOptions) ? newOptions : []
    }
    catch (error) {
      console.error(`动态选项处理失败 [${fieldKey}]:`, error)
      state.fieldStates[fieldKey].error = '加载选项失败'
      state.fieldStates[fieldKey].dynamicOptions = []
    }
    finally {
      state.fieldStates[fieldKey].loading = false
    }
  }

  // 处理动态属性
  function handleDynamicProps(fieldKey: string) {
    const fieldConfig = getFieldConfig(fieldKey)
    if (!fieldConfig?.dynamicProps)
      return

    try {
      const newProps = fieldConfig.dynamicProps(formData)
      state.fieldStates[fieldKey].dynamicProps = newProps || {}
    }
    catch (error) {
      console.error(`动态属性处理失败 [${fieldKey}]:`, error)
      state.fieldStates[fieldKey].dynamicProps = {}
    }
  }

  // 处理字段级联
  function handleFieldCascade(fieldKey: string, newValue: any) {
    const fieldConfig = getFieldConfig(fieldKey)
    if (!fieldConfig?.cascade)
      return

    const { cascadeMap, targetField, forceUpdate = false } = fieldConfig.cascade

    if (cascadeMap[newValue] !== undefined) {
      const targetValue = cascadeMap[newValue]

      // 只有在强制更新或目标字段为空时才更新
      if (forceUpdate || !formData[targetField]) {
        formData[targetField] = targetValue
      }
    }
  }

  // 处理字段监听器
  function handleFieldWatcher(changedFieldKey: string, newValue: any, oldValue: any) {
    const watchers = state.watcherMap[changedFieldKey] || []

    watchers.forEach((watcherFieldKey) => {
      const fieldConfig = getFieldConfig(watcherFieldKey)
      if (fieldConfig?.watcher) {
        try {
          fieldConfig.watcher.handler(changedFieldKey, newValue, oldValue, formData)
        }
        catch (error) {
          console.error(`字段监听器处理失败 [${watcherFieldKey}]:`, error)
        }
      }
    })
  }

  // 获取字段默认值
  function getDefaultValue(field: CurdFormFieldConfig): any {
    if (field.default !== undefined) {
      return field.default
    }

    switch (field.type) {
      case 'checkbox':
      case 'switch':
        return false
      case 'number':
      case 'number-field':
        return 0
      case 'multiselect':
      case 'checkbox-group':
      case 'tags-input':
        return []
      default:
        return ''
    }
  }

  // 处理字段值变化
  async function onFieldChange(fieldKey: string, newValue: any, oldValue: any) {
    // 1. 处理级联更新
    handleFieldCascade(fieldKey, newValue)

    // 2. 处理字段监听器
    handleFieldWatcher(fieldKey, newValue, oldValue)

    // 3. 处理依赖此字段的其他字段的联动
    const dependentFields = state.dependencyMap[fieldKey] || []
    for (const dependentField of dependentFields) {
      await handleFieldLinkage(dependentField)
    }

    // 4. 更新所有字段的动态选项和属性
    await nextTick()
    for (const field of fields) {
      const key = field.key as string

      // 更新动态选项
      if (field.dynamicOptions) {
        await handleDynamicOptions(key)
      }

      // 更新动态属性
      if (field.dynamicProps) {
        handleDynamicProps(key)
      }
    }
  }

  // 初始化所有字段的联动
  async function initializeLinkage() {
    for (const field of fields) {
      const key = field.key as string

      console.log(field, field.linkage)
      // 初始化有联动配置的字段
      if (field.linkage) {
        await handleFieldLinkage(key)
      }

      // 初始化动态选项
      if (field.dynamicOptions) {
        await handleDynamicOptions(key)
      }

      // 初始化动态属性
      if (field.dynamicProps) {
        handleDynamicProps(key)
      }
    }
  }

  // 获取字段的实际选项（优先使用动态选项）
  function getFieldOptions(fieldKey: string): FormFieldOptions {
    const fieldState = state.fieldStates[fieldKey]
    const fieldConfig = getFieldConfig(fieldKey)

    // 优先使用动态选项
    if (fieldState?.dynamicOptions?.length > 0) {
      return fieldState.dynamicOptions
    }

    // 回退到静态选项
    return fieldConfig?.componentProps?.options || []
  }

  // 获取字段状态
  function getFieldState(fieldKey: string): FieldState {
    return state.fieldStates[fieldKey] || {
      loading: false,
      error: null,
      dynamicOptions: [],
      dynamicProps: {},
    }
  }

  // 获取字段的动态属性
  function getFieldDynamicProps(fieldKey: string): Record<string, any> {
    return state.fieldStates[fieldKey]?.dynamicProps || {}
  }

  // 清理资源
  function cleanup() {
    Object.values(state.debounceTimers).forEach(timer => clearTimeout(timer))
    state.debounceTimers = {}
  }

  // 初始化
  initFieldStates()

  return {
    // 状态
    fieldStates: state.fieldStates,

    // 方法
    onFieldChange,
    initializeLinkage,
    getFieldOptions,
    getFieldState,
    getFieldDynamicProps,
    cleanup,
  }
}
