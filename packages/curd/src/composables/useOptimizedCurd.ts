/**
 * 优化后的 useCurd Hook - 提升性能和类型安全
 */

import type { Ref } from 'vue'
import type { StrictCurdApi, StrictListResponse } from '../types/strict'
import { computed, ref, watch } from 'vue'
import { useErrorBoundary, withRetry } from '../utils/error-handler'
import { useI18n } from '../utils/i18n'
import { CacheManager, useDebounce } from '../utils/performance'

export interface OptimizedCurdConfig<T> {
  api: StrictCurdApi<T>
  primaryKey?: keyof T
  pageSize?: number
  enableCache?: boolean
  cacheTTL?: number
  enableVirtualScroll?: boolean
  debounceDelay?: number
  retryConfig?: {
    maxAttempts: number
    delay: number
  }
}

export function useOptimizedCurd<T extends Record<string, any>>(
  config: OptimizedCurdConfig<T>,
) {
  const { t } = useI18n()
  const { handleError, withErrorHandling } = useErrorBoundary()

  // 配置默认值
  const {
    api,
    primaryKey = 'id' as keyof T,
    pageSize = 20,
    enableCache = true,
    cacheTTL = 5 * 60 * 1000, // 5分钟
    debounceDelay = 300,
    retryConfig = { maxAttempts: 3, delay: 1000 },
  } = config

  // 状态管理
  const loading = ref(false)
  const data = ref<T[]>([]) as Ref<T[]>
  const total = ref(0)
  const currentPage = ref(1)
  const searchParams = ref<Record<string, any>>({})
  const selectedIds = ref<(string | number)[]>([])
  const sortField = ref<string>('')
  const sortOrder = ref<'asc' | 'desc'>('asc')

  // 缓存管理
  const cache = enableCache ? new CacheManager<StrictListResponse<T>>() : null

  // 防抖搜索参数
  const debouncedSearchParams = useDebounce(searchParams, debounceDelay)

  // 计算属性
  const selectedRows = computed(() =>
    data.value.filter(item =>
      selectedIds.value.includes(item[primaryKey] as string | number),
    ),
  )

  const hasSelection = computed(() => selectedIds.value.length > 0)

  const pagination = computed(() => ({
    current: currentPage.value,
    pageSize,
    total: total.value,
    totalPages: Math.ceil(total.value / pageSize),
  }))

  // 生成缓存键
  const generateCacheKey = (params: Record<string, any>) => {
    return JSON.stringify({
      page: currentPage.value,
      pageSize,
      sort: { field: sortField.value, order: sortOrder.value },
      search: params,
    })
  }

  // 获取列表数据
  const fetchList = withErrorHandling(async (useCache = true) => {
    loading.value = true

    try {
      const params = {
        page: currentPage.value,
        page_size: pageSize,
        sortby: sortField.value,
        order: sortOrder.value,
        ...debouncedSearchParams.value,
      }

      const cacheKey = generateCacheKey(params)

      // 尝试从缓存获取
      if (useCache && cache) {
        const cached = cache.get(cacheKey)
        if (cached) {
          data.value = cached.data
          total.value = cached.pagination.total
          return cached
        }
      }

      // 发起 API 请求（带重试机制）
      const response = await withRetry(
        () => api.getList(params),
        retryConfig,
      )

      if (response.success) {
        data.value = response.data
        total.value = response.pagination.total

        // 缓存结果
        if (cache) {
          cache.set(cacheKey, response, cacheTTL)
        }

        return response
      }
      else {
        throw new Error(response.message || t('message.operationFailed'))
      }
    }
    finally {
      loading.value = false
    }
  }, { operation: 'fetchList' })

  // 创建记录
  const createItem = withErrorHandling(async (itemData: Partial<T>) => {
    loading.value = true

    try {
      const response = await withRetry(
        () => api.createItem(itemData),
        retryConfig,
      )

      if (response.success) {
        // 清除缓存
        cache?.clear()

        // 刷新列表
        await fetchList(false)

        return response.data
      }
      else {
        throw new Error(response.message || t('message.operationFailed'))
      }
    }
    finally {
      loading.value = false
    }
  }, { operation: 'createItem' })

  // 更新记录
  const updateItem = withErrorHandling(async (id: string | number, itemData: Partial<T>) => {
    loading.value = true

    try {
      const response = await withRetry(
        () => api.updateItem(id, itemData),
        retryConfig,
      )

      if (response.success) {
        // 清除缓存
        cache?.clear()

        // 更新本地数据
        const index = data.value.findIndex(item => item[primaryKey] === id)
        if (index !== -1) {
          data.value[index] = { ...data.value[index], ...response.data }
        }

        return response.data
      }
      else {
        throw new Error(response.message || t('message.operationFailed'))
      }
    }
    finally {
      loading.value = false
    }
  }, { operation: 'updateItem' })

  // 删除记录
  const deleteItem = withErrorHandling(async (id: string | number) => {
    loading.value = true

    try {
      const response = await withRetry(
        () => api.deleteItem(id),
        retryConfig,
      )

      if (response.success) {
        // 清除缓存
        cache?.clear()

        // 从本地数据中移除
        const index = data.value.findIndex(item => item[primaryKey] === id)
        if (index !== -1) {
          data.value.splice(index, 1)
          total.value--
        }

        // 清除选择
        const selectedIndex = selectedIds.value.indexOf(id)
        if (selectedIndex !== -1) {
          selectedIds.value.splice(selectedIndex, 1)
        }

        return response.data
      }
      else {
        throw new Error(response.message || t('message.operationFailed'))
      }
    }
    finally {
      loading.value = false
    }
  }, { operation: 'deleteItem' })

  // 批量删除
  const batchDelete = withErrorHandling(async (ids: (string | number)[]) => {
    loading.value = true

    try {
      // 并发删除（可以根据需要调整并发数）
      const deletePromises = ids.map(id => api.deleteItem(id))
      const responses = await Promise.allSettled(deletePromises)

      const successCount = responses.filter(r => r.status === 'fulfilled').length
      const failedCount = responses.length - successCount

      if (successCount > 0) {
        // 清除缓存
        cache?.clear()

        // 刷新列表
        await fetchList(false)

        // 清除选择
        selectedIds.value = []
      }

      return { successCount, failedCount }
    }
    finally {
      loading.value = false
    }
  }, { operation: 'batchDelete' })

  // 搜索
  const search = (params: Record<string, any>) => {
    searchParams.value = { ...params }
    currentPage.value = 1
  }

  // 排序
  const sort = (field: string, order: 'asc' | 'desc') => {
    sortField.value = field
    sortOrder.value = order
    currentPage.value = 1
  }

  // 分页
  const changePage = (page: number) => {
    currentPage.value = page
  }

  // 选择管理
  const toggleSelection = (id: string | number) => {
    const index = selectedIds.value.indexOf(id)
    if (index === -1) {
      selectedIds.value.push(id)
    }
    else {
      selectedIds.value.splice(index, 1)
    }
  }

  const selectAll = () => {
    selectedIds.value = data.value.map(item => item[primaryKey] as string | number)
  }

  const clearSelection = () => {
    selectedIds.value = []
  }

  // 刷新
  const refresh = () => {
    cache?.clear()
    return fetchList(false)
  }

  // 监听搜索参数变化
  watch(debouncedSearchParams, () => {
    currentPage.value = 1
    fetchList()
  }, { deep: true })

  // 监听分页和排序变化
  watch([currentPage, sortField, sortOrder], () => {
    fetchList()
  })

  // 初始化加载
  fetchList()

  return {
    // 状态
    loading,
    data,
    total,
    currentPage,
    selectedIds,
    selectedRows,
    hasSelection,
    pagination,

    // 操作方法
    fetchList,
    createItem,
    updateItem,
    deleteItem,
    batchDelete,
    search,
    sort,
    changePage,
    toggleSelection,
    selectAll,
    clearSelection,
    refresh,

    // 工具方法
    handleError,
  }
}
