# Timestamp 字段使用示例

## 基本用法

DateField 组件现在支持 timestamp 类型，可以接收和返回时间戳格式的数据。

### 1. 基本 timestamp 字段

```typescript
const field: CurdFormFieldConfig = {
  key: 'created_at',
  label: '创建时间',
  type: 'timestamp',
  required: true
}
```

### 2. 配置时间戳格式（毫秒）

```typescript
const field: CurdFormFieldConfig = {
  key: 'created_at',
  label: '创建时间',
  type: 'timestamp',
  componentProps: {
    timestampConfig: {
      format: 'milliseconds' // 默认值，返回毫秒时间戳
    }
  }
}
```

### 3. 配置时间戳格式（秒）

```typescript
const field: CurdFormFieldConfig = {
  key: 'created_at',
  label: '创建时间',
  type: 'timestamp',
  componentProps: {
    timestampConfig: {
      format: 'seconds' // 返回秒时间戳
    }
  }
}
```

### 4. 完整配置示例

```typescript
const field: CurdFormFieldConfig = {
  key: 'event_time',
  label: '事件时间',
  type: 'timestamp',
  required: true,
  componentProps: {
    timestampConfig: {
      format: 'seconds',
      minTimestamp: 1640995200, // 2022-01-01 00:00:00 (秒)
      maxTimestamp: 1672531199, // 2022-12-31 23:59:59 (秒)
      displayFormat: 'YYYY-MM-DD HH:mm:ss',
      timezone: 'Asia/Shanghai'
    }
  }
}
```

## 数据流

### 输入数据
- **number**: 时间戳（毫秒或秒，根据配置自动识别）
- **DateValue**: 标准日期值对象
- **null**: 空值

### 输出数据
- **number**: 时间戳（根据 `timestampConfig.format` 配置返回毫秒或秒）
- **0**: 空值时返回 0

## 兼容性

该组件完全向后兼容：
- 现有的 `date`、`datetime`、`time` 类型继续正常工作
- 新增的 `timestamp` 类型提供额外的时间戳支持
- 可以在同一个表单中混合使用不同的日期时间字段类型

## 使用场景

1. **API 数据对接**: 后端返回时间戳格式的数据
2. **数据库时间字段**: 存储为 Unix 时间戳的字段
3. **日志记录**: 需要精确时间戳的场景
4. **事件追踪**: 记录用户行为的时间点