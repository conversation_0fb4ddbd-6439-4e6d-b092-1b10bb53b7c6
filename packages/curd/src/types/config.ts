import type { Ref } from 'vue'
import type { CurdApi, StdColumn } from './api'
import type { CodeEditorConfig, DateRangeConfig, FieldCascadeConfig, FieldLinkageConfig, FieldWatchConfig, FileUploadConfig, FormFieldOptions, FormFieldType, RemoteSearchConfig, RichTextConfig, SliderConfig } from './form'
import type { CurdHooks } from './hooks'

// 按钮文本配置
export interface ButtonText {
  create?: string
  edit?: string
  delete?: string
  batchDelete?: string
  search?: string
  refresh?: string
  export?: string
  import?: string
}

// 字段选项配置
export interface FieldOption {
  label: string
  value: any
  disabled?: boolean
}

// 表格显示配置
export interface TableFieldConfig {
  /** 是否在表格中显示 */
  show?: boolean
  /** 自定义插槽名称 */
  slot?: string
  /** 列宽 */
  width?: number | string
  /** 是否可排序 */
  sortable?: boolean
  /** 是否固定列 */
  fixed?: 'left' | 'right'
  /** 自定义渲染函数 */
  render?: (value: any, record: any) => any
}

// 表单显示配置（重命名以避免冲突）
export interface CurdFormFieldConfig {
  /** 字段键名，对应接口字段 */
  key?: string
  /** 字段中文名，用于显示 */
  label?: string
  /** 渲染类型 */
  type?: FormFieldType

  /** 是否在表单中显示 */
  show?: boolean | ((formData: Record<string, any>) => boolean)
  /** 是否必填 */
  required?: boolean
  /** 默认值 */
  default?: any
  /** 是否禁用 */
  disabled?: boolean | ((formData: Record<string, any>) => boolean)
  /** 验证规则 */
  rules?: Array<{
    required?: boolean
    message?: string
    min?: number
    max?: number
    pattern?: RegExp
    validator?: (value: any, formData: any) => boolean | string
  }>
  /** 列宽配置（1-12，对应 grid-cols） */
  col?: number

  // === 字段联动相关配置 ===

  /** 字段联动配置 - 根据其他字段值动态更新选项 */
  linkage?: FieldLinkageConfig

  /** 字段值变化监听器 - 监听其他字段变化并执行自定义逻辑 */
  watcher?: FieldWatchConfig

  /** 字段级联配置 - 值变化时自动设置其他字段的值 */
  cascade?: FieldCascadeConfig

  /** 动态选项函数 - 基于表单数据动态生成选项 */
  dynamicOptions?: (formData: Record<string, any>) =>
    Promise<{ label: string, value: any, [key: string]: any }[]> | { label: string, value: any, [key: string]: any }[]

  /** 动态属性函数 - 基于表单数据动态设置字段属性 */
  dynamicProps?: (formData: Record<string, any>) => Record<string, any>

  /** 组件特定属性 */
  componentProps?: {
    /** 占位符 */
    placeholder?: string

    // 基础选项配置（select, radio-group, checkbox-group 等）
    options?: FormFieldOptions

    /** combobox 远程搜索配置 */
    remoteSearch?: RemoteSearchConfig

    /** 文件上传配置 */
    fileConfig?: FileUploadConfig

    /** 日期范围配置 */
    dateRangeConfig?: DateRangeConfig

    /** 时间戳配置 */
    timestampConfig?: {
      /** 时间戳格式：毫秒或秒 */
      format?: 'milliseconds' | 'seconds'
      /** 最小时间戳 */
      minTimestamp?: number
      /** 最大时间戳 */
      maxTimestamp?: number
      /** 显示格式 */
      displayFormat?: string
      /** 时区 */
      timezone?: string
    }

    /** 滑块配置 */
    sliderConfig?: SliderConfig

    /** 富文本编辑器配置 */
    richTextConfig?: RichTextConfig

    /** 代码编辑器配置 */
    codeConfig?: CodeEditorConfig

    /** 数字字段配置 */
    numberConfig?: {
      min?: number
      max?: number
      step?: number
      precision?: number
      formatOptions?: Intl.NumberFormatOptions
    }

    /** 标签输入配置 */
    tagsConfig?: {
    /** 最大标签数 */
      maxTags?: number
      /** 允许重复标签 */
      allowDuplicates?: boolean
      /** 标签验证函数 */
      validateTag?: (tag: string) => boolean
      /** 自动完成选项 */
      suggestions?: string[]
    }

    /** PIN 码输入配置 */
    pinConfig?: {
    /** PIN 码长度 */
      length?: number
      /** 是否遮蔽输入 */
      mask?: boolean
      /** 分隔符 */
      separator?: string
    }

    /** 评分组件配置 */
    ratingConfig?: {
    /** 最大评分 */
      max?: number
      /** 是否允许半星 */
      allowHalf?: boolean
      /** 自定义图标 */
      icon?: string
      /** 是否只读 */
      readonly?: boolean
    }

    /** 颜色选择器配置 */
    colorConfig?: {
    /** 颜色格式 */
      format?: 'hex' | 'rgb' | 'hsl'
      /** 预设颜色 */
      presetColors?: string[]
      /** 是否显示透明度 */
      showAlpha?: boolean
    }

    /** 自定义组件配置 */
    customComponent?: {
    /** 组件名称或组件实例 */
      component: any
      /** 传递给自定义组件的 props */
      props?: Record<string, any>
      /** 自定义事件映射 */
      events?: Record<string, string>
    }

    // [key: string]: any
  }
}

// 搜索配置
export interface SearchFieldConfig {
  /** 是否支持搜索 */
  show?: boolean
  /** 搜索类型，如果不指定则使用字段的 type */
  type?: FormFieldType
  /** 搜索占位符 */
  placeholder?: string
  /** 搜索组件特定属性 */
  componentProps?: Record<string, any>
}

// 统一字段配置（字段驱动的核心）
export interface CurdFieldConfig<T = any> {
  /** 字段键名，对应接口字段 */
  key: keyof T
  /** 字段中文名，用于显示 */
  label: string
  /** 渲染类型 */
  type: FormFieldType
  /** 下拉等类型的选项配置 */
  options?: ReadonlyArray<FieldOption>
  /** 占位符 */
  placeholder?: string

  /** 表格展示相关配置 */
  table?: TableFieldConfig | boolean
  /** 表单展示相关配置 */
  form?: CurdFormFieldConfig | boolean
  /** 搜索相关配置 */
  search?: SearchFieldConfig | boolean

  /** 权限标识 */
  permission?: string
  /** 字段描述/帮助文本 */
  description?: string
  /** 字段分组 */
  group?: string
}

// 操作按钮配置
export interface ActionsConfig {
  /** 是否显示新增按钮 */
  add?: boolean
  /** 是否显示编辑按钮 */
  edit?: boolean
  /** 是否显示删除按钮 */
  delete?: boolean
  /** 是否显示批量删除按钮 */
  batchDelete?: boolean
  /** 是否显示导出按钮 */
  export?: boolean
  /** 是否显示导入按钮 */
  import?: boolean
  /** 自定义操作按钮 */
  custom?: Array<{
    key: string
    label: string
    icon?: string
    type?: 'primary' | 'default' | 'danger'
    permission?: string
    handler: (record?: any, selectedRows?: any[]) => void
  }>
  /** 是否显示列按钮 */
  col?: boolean
}

// 新的统一 CRUD 配置（基于设计文档）
export interface CurdConfig<T = any> {
  /** 页面标题 */
  title: string
  /** API 接口 */
  api: CurdApi<T>
  /** 字段配置数组（核心驱动） */
  fields: CurdFieldConfig<T>[]

  /** 操作按钮配置 */
  actions?: ActionsConfig | boolean
  /** 是否启用分页 */
  pagination?: boolean
  /** 每页条数 */
  pageSize?: number
  /** 每页条数选项 */
  pageSizeOptions?: number[]

  /** 主键字段名 */
  primaryKey?: string
  /** 按钮文本配置 */
  btnText?: ButtonText

  /** 消息配置 */
  confirmMessages?: {
    delete?: string
    batchDelete?: string
  }

  successMessages?: {
    create?: string
    update?: string
    delete?: string
    batchDelete?: string
  }

  /** hooks 钩子函数 */
  hooks?: CurdHooks<T>

  /** 权限配置 */
  permissions?: {
    create?: string
    edit?: string
    delete?: string
    batchDelete?: string
    export?: string
    import?: string
  }
}

export interface UseCurdOptions<T = any> {
  api: CurdApi<T>
  columns: StdColumn<T>[]
  pageSize?: number
  pageSizeOptions?: number[]
  searchable?: boolean
  creatable?: boolean
  editable?: boolean
  deletable?: boolean
  batchDeletable?: boolean
  hooks?: CurdHooks<T>
  immediate?: boolean
  primaryKey?: string
}

export interface UseCurdReturn<T = any> {
  data: Ref<T[]>
  loading: Ref<boolean>
  error: Ref<Error | null>
  total: Ref<number>
  currentPage: Ref<number>
  currentPageSize: Ref<number>
  totalPages: Ref<number>

  selectedRows: Ref<T[]>
  selectedRowKeys: Ref<(string | number)[]>

  searchParams: Ref<Record<string, any>>
  sortParams: Ref<Record<string, any>>

  refresh: () => Promise<void>
  search: (params: Record<string, any>) => Promise<void>
  sort: (field: string, order: 'asc' | 'desc') => Promise<void>
  changePage: (page: number) => Promise<void>
  changePageSize: (size: number) => Promise<void>
  create: (formData: Partial<T>) => Promise<T>
  update: (id: string | number, formData: Partial<T>) => Promise<T>
  remove: (id: string | number, permanently?: boolean) => Promise<void>
  batchRemove: (ids: (string | number)[], permanently?: boolean) => Promise<void>
  clearSelection: () => void
}
