/**
 * 统一错误处理和日志系统
 */

// 错误类型定义
export enum ErrorType {
  VALIDATION = 'validation',
  API = 'api',
  NETWORK = 'network',
  PERMISSION = 'permission',
  UNKNOWN = 'unknown',
}

export interface CurdError {
  type: ErrorType
  code?: string | number
  message: string
  details?: unknown
  timestamp: number
  context?: Record<string, unknown>
}

// 错误处理器
export class ErrorHandler {
  private static instance: ErrorHandler
  private errorListeners: Array<(error: CurdError) => void> = []

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler()
    }
    return ErrorHandler.instance
  }

  // 注册错误监听器
  onError(listener: (error: CurdError) => void) {
    this.errorListeners.push(listener)
    return () => {
      const index = this.errorListeners.indexOf(listener)
      if (index > -1) {
        this.errorListeners.splice(index, 1)
      }
    }
  }

  // 处理错误
  handleError(error: unknown, context?: Record<string, unknown>): CurdError {
    const curdError = this.normalizeError(error, context)

    // 通知监听器
    this.errorListeners.forEach((listener) => {
      try {
        listener(curdError)
      }
      catch (e) {
        console.error('Error in error listener:', e)
      }
    })

    return curdError
  }

  private normalizeError(error: unknown, context?: Record<string, unknown>): CurdError {
    const timestamp = Date.now()

    if (error instanceof Error) {
      // 网络错误
      if (error.name === 'NetworkError' || error.message.includes('fetch')) {
        return {
          type: ErrorType.NETWORK,
          message: '网络连接失败，请检查网络设置',
          details: error,
          timestamp,
          context,
        }
      }

      // API 错误
      if ('response' in error) {
        const response = (error as any).response
        return {
          type: ErrorType.API,
          code: response?.status,
          message: response?.data?.message || error.message,
          details: response?.data,
          timestamp,
          context,
        }
      }

      // 验证错误
      if (error.name === 'ValidationError') {
        return {
          type: ErrorType.VALIDATION,
          message: error.message,
          details: error,
          timestamp,
          context,
        }
      }

      // 权限错误
      if (error.message.includes('permission') || error.message.includes('unauthorized')) {
        return {
          type: ErrorType.PERMISSION,
          message: '权限不足，请联系管理员',
          details: error,
          timestamp,
          context,
        }
      }

      return {
        type: ErrorType.UNKNOWN,
        message: error.message,
        details: error,
        timestamp,
        context,
      }
    }

    return {
      type: ErrorType.UNKNOWN,
      message: String(error),
      details: error,
      timestamp,
      context,
    }
  }
}

// 错误边界 Hook
export function useErrorBoundary() {
  const errorHandler = ErrorHandler.getInstance()

  const handleError = (error: unknown, context?: Record<string, unknown>) => {
    return errorHandler.handleError(error, context)
  }

  const withErrorHandling = <T extends (...args: any[]) => any>(
    fn: T,
    context?: Record<string, unknown>,
  ): T => {
    return ((...args: Parameters<T>) => {
      try {
        const result = fn(...args)

        // 处理 Promise 错误
        if (result instanceof Promise) {
          return result.catch((error) => {
            handleError(error, context)
            throw error
          })
        }

        return result
      }
      catch (error) {
        handleError(error, context)
        throw error
      }
    }) as T
  }

  return {
    handleError,
    withErrorHandling,
  }
}

// 重试机制
export interface RetryConfig {
  maxAttempts: number
  delay: number
  backoff?: 'linear' | 'exponential'
  retryCondition?: (error: unknown) => boolean
}

export async function withRetry<T>(
  fn: () => Promise<T>,
  config: RetryConfig,
): Promise<T> {
  const { maxAttempts, delay, backoff = 'linear', retryCondition } = config
  let lastError: unknown

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn()
    }
    catch (error) {
      lastError = error

      // 检查是否应该重试
      if (retryCondition && !retryCondition(error)) {
        throw error
      }

      // 最后一次尝试失败
      if (attempt === maxAttempts) {
        throw error
      }

      // 计算延迟时间
      const waitTime = backoff === 'exponential'
        ? delay * 2 ** (attempt - 1)
        : delay * attempt

      await new Promise(resolve => setTimeout(resolve, waitTime))
    }
  }

  throw lastError
}
