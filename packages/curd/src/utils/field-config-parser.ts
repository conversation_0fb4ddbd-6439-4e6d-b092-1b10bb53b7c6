import type { ColumnDef } from '@tanstack/vue-table'
import type { ActionsConfig, CurdFieldConfig, CurdFormFieldConfig, TableFieldConfig } from '../types/config'

/**
 * 字段配置解析器
 * 根据统一的字段配置生成表格列、表单字段和搜索字段配置
 */
export class FieldConfigParser {
  /**
   * 从字段配置生成表格列配置
   */
  static parseTableColumns<T = any>(fields: CurdFieldConfig<T>[], actions?: ActionsConfig | boolean): ColumnDef<T>[] {
    const columns = fields
      .filter((field) => {
        if (!field.table)
          return false
        if (field.table === true || (typeof field.table === 'object'))
          return true
        return !!field.table
      })
      .map((field) => {
        const tableConfig = field.table as TableFieldConfig | boolean | undefined
        const config: ColumnDef<T> = {
          id: field.key as string,
          accessorKey: field.key as string,
          header: field.label,
        }

        // 如果是对象配置，应用详细配置
        if (tableConfig && typeof tableConfig === 'object') {
          if (tableConfig.width) {
            config.size = typeof tableConfig.width === 'number' ? tableConfig.width : undefined
          }
          if (tableConfig.sortable !== undefined) {
            config.enableSorting = tableConfig.sortable
          }
          if (tableConfig.render) {
            config.cell = ({ getValue, row }) => tableConfig.render!(getValue(), row.original)
          }
        }

        return config
      })
    if (typeof actions === 'object' && actions.col !== false) {
      return columns.concat({
        id: 'actions',
        accessorKey: 'actions',
        header: '操作',
      })
    }
    return columns
  }

  /**
   * 从字段配置生成表单字段配置
   */
  static parseFormFields<T = any>(fields: CurdFieldConfig<T>[]): CurdFormFieldConfig[] {
    return fields
      .filter((field) => {
        // 如果 form 配置为 false，则不显示
        if (!field.form)
          return false
        // 如果 form 配置为 true 或对象，则显示
        if (field.form || (typeof field.form === 'object'))
          return true
        return field.form
      })
      .map((field) => {
        const formConfig = field.form
        const fieldConfig: CurdFormFieldConfig = {
          key: field.key as string,
          label: field.label,
          type: field.type,
          componentProps: {},
        }

        // 如果是对象配置，应用详细配置
        if (formConfig && typeof formConfig === 'object') {
          Object.assign(fieldConfig, formConfig)

          if (field.options && !fieldConfig.componentProps?.options) {
            fieldConfig.componentProps!.options = field.options
          }
        }

        return fieldConfig
      })
  }

  /**
   * 从字段配置生成搜索字段配置
   */
  static parseSearchFields<T = any>(fields: CurdFieldConfig<T>[]): CurdFormFieldConfig[] {
    return fields
      .filter((field) => {
        // 如果 search 配置为 false，则不显示
        if (!field.search)
          return false
        // 如果 search 配置为 true 或对象，则显示
        if (field.search || (typeof field.search === 'object'))
          return true
        // 默认不显示搜索
        return false
      })
      .map((field) => {
        const searchConfig = field.search
        const fieldConfig: CurdFormFieldConfig = {
          key: field.key as string,
          label: field.label,
          type: field.type,
          componentProps: {
            placeholder: field.placeholder || `请输入${field.label}`,
            options: field.options,
          },
        }

        // 如果是对象配置，应用详细配置
        if (searchConfig && typeof searchConfig === 'object') {
          if (searchConfig.type) {
            fieldConfig.type = searchConfig.type
          }
          if (searchConfig.componentProps) {
            fieldConfig.componentProps = searchConfig.componentProps
          }
        }

        // 搜索字段通常不是必填的
        fieldConfig.required = false

        return fieldConfig
      })
  }

  /**
   * 解析完整的字段配置
   */
  static parseFields<T = any>(fields: CurdFieldConfig<T>[], actions?: ActionsConfig | boolean) {
    return {
      tableColumns: this.parseTableColumns(fields, actions),
      formFields: this.parseFormFields(fields),
      searchFields: this.parseSearchFields(fields),
    }
  }
}

/**
 * 便捷函数：解析字段配置
 */
export function parseFieldConfig<T = any>(fields: CurdFieldConfig<T>[], actions?: ActionsConfig | boolean) {
  return FieldConfigParser.parseFields(fields, actions)
}

/**
 * 便捷函数：从字段配置生成表格列
 */
export function fieldsToTableColumns<T = any>(fields: CurdFieldConfig<T>[], actions?: ActionsConfig | boolean): ColumnDef<T>[] {
  return FieldConfigParser.parseTableColumns(fields, actions)
}

/**
 * 便捷函数：从字段配置生成表单字段
 */
export function fieldsToFormFields<T = any>(fields: CurdFieldConfig<T>[]): CurdFormFieldConfig[] {
  return FieldConfigParser.parseFormFields(fields)
}

/**
 * 便捷函数：从字段配置生成搜索字段
 */
export function fieldsToSearchFields<T = any>(fields: CurdFieldConfig<T>[]): CurdFormFieldConfig[] {
  return FieldConfigParser.parseSearchFields(fields)
}
