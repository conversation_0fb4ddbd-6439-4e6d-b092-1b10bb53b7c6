/**
 * 性能优化工具集
 */

import type { Ref } from 'vue'
import { computed, ref, watch } from 'vue'

// 防抖 Hook
export function useDebounce<T>(value: Ref<T>, delay: number = 300) {
  const debouncedValue = ref(value.value) as Ref<T>
  let timeoutId: number

  watch(value, (newValue) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => {
      debouncedValue.value = newValue
    }, delay)
  }, { immediate: true })

  return debouncedValue
}

// 节流 Hook
export function useThrottle<T>(value: Ref<T>, delay: number = 300) {
  const throttledValue = ref(value.value) as Ref<T>
  let lastUpdate = 0

  watch(value, (newValue) => {
    const now = Date.now()
    if (now - lastUpdate >= delay) {
      throttledValue.value = newValue
      lastUpdate = now
    }
  }, { immediate: true })

  return throttledValue
}

// 虚拟滚动配置
export interface VirtualScrollConfig {
  itemHeight: number
  containerHeight: number
  overscan?: number
  threshold?: number
}

// 虚拟滚动 Hook
export function useVirtualScroll<T>(
  items: Ref<T[]>,
  config: VirtualScrollConfig,
) {
  const scrollTop = ref(0)
  const { itemHeight, containerHeight, overscan = 5 } = config

  const visibleRange = computed(() => {
    const start = Math.floor(scrollTop.value / itemHeight)
    const end = Math.min(
      start + Math.ceil(containerHeight / itemHeight) + overscan,
      items.value.length,
    )
    return { start: Math.max(0, start - overscan), end }
  })

  const visibleItems = computed(() => {
    const { start, end } = visibleRange.value
    return items.value.slice(start, end).map((item, index) => ({
      item,
      index: start + index,
      top: (start + index) * itemHeight,
    }))
  })

  const totalHeight = computed(() => items.value.length * itemHeight)

  return {
    scrollTop,
    visibleItems,
    visibleRange,
    totalHeight,
    setScrollTop: (top: number) => { scrollTop.value = top },
  }
}

// 缓存管理器
export class CacheManager<T> {
  private cache = new Map<string, { data: T, timestamp: number, ttl: number }>()

  set(key: string, data: T, ttl: number = 5 * 60 * 1000) {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    })
  }

  get(key: string): T | null {
    const item = this.cache.get(key)
    if (!item)
      return null

    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key)
      return null
    }

    return item.data
  }

  clear() {
    this.cache.clear()
  }

  delete(key: string) {
    this.cache.delete(key)
  }
}

// 智能计算缓存
export function useComputedCache<T>(
  getter: () => T,
  deps: Ref<unknown>[],
  ttl: number = 1000,
) {
  const cache = new Map<string, { value: T, timestamp: number }>()

  return computed(() => {
    const key = JSON.stringify(deps.map(dep => dep.value))
    const cached = cache.get(key)
    const now = Date.now()

    if (cached && now - cached.timestamp < ttl) {
      return cached.value
    }

    const value = getter()
    cache.set(key, { value, timestamp: now })

    // 清理过期缓存
    if (cache.size > 100) {
      for (const [k, v] of cache.entries()) {
        if (now - v.timestamp > ttl) {
          cache.delete(k)
        }
      }
    }

    return value
  })
}
