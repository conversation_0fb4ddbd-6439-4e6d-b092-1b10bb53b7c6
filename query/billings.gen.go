// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/potato-billing-api/model"
)

func newBilling(db *gorm.DB, opts ...gen.DOOption) billing {
	_billing := billing{}

	_billing.billingDo.UseDB(db, opts...)
	_billing.billingDo.UseModel(&model.Billing{})

	tableName := _billing.billingDo.TableName()
	_billing.ALL = field.NewAsterisk(tableName)
	_billing.ID = field.NewUint64(tableName, "id")
	_billing.CreatedAt = field.NewInt64(tableName, "created_at")
	_billing.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_billing.DeletedAt = field.NewUint(tableName, "deleted_at")
	_billing.AppID = field.NewUint64(tableName, "app_id")
	_billing.UserID = field.NewUint64(tableName, "user_id")
	_billing.Module = field.NewString(tableName, "module")
	_billing.Cost = field.NewField(tableName, "cost")
	_billing.Usage = field.NewInt64(tableName, "usage")
	_billing.Calls = field.NewInt64(tableName, "calls")
	_billing.App = billingBelongsToApp{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("App", "model.App"),
		User: struct {
			field.RelationField
			Avatar struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("App.User", "model.User"),
			Avatar: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("App.User.Avatar", "model.Upload"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("App.User.Avatar.User", "model.User"),
				},
			},
		},
	}

	_billing.User = billingBelongsToUser{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("User", "model.User"),
	}

	_billing.fillFieldMap()

	return _billing
}

type billing struct {
	billingDo

	ALL       field.Asterisk
	ID        field.Uint64
	CreatedAt field.Int64
	UpdatedAt field.Int64
	DeletedAt field.Uint
	AppID     field.Uint64
	UserID    field.Uint64
	Module    field.String
	Cost      field.Field
	Usage     field.Int64
	Calls     field.Int64
	App       billingBelongsToApp

	User billingBelongsToUser

	fieldMap map[string]field.Expr
}

func (b billing) Table(newTableName string) *billing {
	b.billingDo.UseTable(newTableName)
	return b.updateTableName(newTableName)
}

func (b billing) As(alias string) *billing {
	b.billingDo.DO = *(b.billingDo.As(alias).(*gen.DO))
	return b.updateTableName(alias)
}

func (b *billing) updateTableName(table string) *billing {
	b.ALL = field.NewAsterisk(table)
	b.ID = field.NewUint64(table, "id")
	b.CreatedAt = field.NewInt64(table, "created_at")
	b.UpdatedAt = field.NewInt64(table, "updated_at")
	b.DeletedAt = field.NewUint(table, "deleted_at")
	b.AppID = field.NewUint64(table, "app_id")
	b.UserID = field.NewUint64(table, "user_id")
	b.Module = field.NewString(table, "module")
	b.Cost = field.NewField(table, "cost")
	b.Usage = field.NewInt64(table, "usage")
	b.Calls = field.NewInt64(table, "calls")

	b.fillFieldMap()

	return b
}

func (b *billing) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := b.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (b *billing) fillFieldMap() {
	b.fieldMap = make(map[string]field.Expr, 12)
	b.fieldMap["id"] = b.ID
	b.fieldMap["created_at"] = b.CreatedAt
	b.fieldMap["updated_at"] = b.UpdatedAt
	b.fieldMap["deleted_at"] = b.DeletedAt
	b.fieldMap["app_id"] = b.AppID
	b.fieldMap["user_id"] = b.UserID
	b.fieldMap["module"] = b.Module
	b.fieldMap["cost"] = b.Cost
	b.fieldMap["usage"] = b.Usage
	b.fieldMap["calls"] = b.Calls

}

func (b billing) clone(db *gorm.DB) billing {
	b.billingDo.ReplaceConnPool(db.Statement.ConnPool)
	b.App.db = db.Session(&gorm.Session{Initialized: true})
	b.App.db.Statement.ConnPool = db.Statement.ConnPool
	b.User.db = db.Session(&gorm.Session{Initialized: true})
	b.User.db.Statement.ConnPool = db.Statement.ConnPool
	return b
}

func (b billing) replaceDB(db *gorm.DB) billing {
	b.billingDo.ReplaceDB(db)
	b.App.db = db.Session(&gorm.Session{})
	b.User.db = db.Session(&gorm.Session{})
	return b
}

type billingBelongsToApp struct {
	db *gorm.DB

	field.RelationField

	User struct {
		field.RelationField
		Avatar struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
	}
}

func (a billingBelongsToApp) Where(conds ...field.Expr) *billingBelongsToApp {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a billingBelongsToApp) WithContext(ctx context.Context) *billingBelongsToApp {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a billingBelongsToApp) Session(session *gorm.Session) *billingBelongsToApp {
	a.db = a.db.Session(session)
	return &a
}

func (a billingBelongsToApp) Model(m *model.Billing) *billingBelongsToAppTx {
	return &billingBelongsToAppTx{a.db.Model(m).Association(a.Name())}
}

func (a billingBelongsToApp) Unscoped() *billingBelongsToApp {
	a.db = a.db.Unscoped()
	return &a
}

type billingBelongsToAppTx struct{ tx *gorm.Association }

func (a billingBelongsToAppTx) Find() (result *model.App, err error) {
	return result, a.tx.Find(&result)
}

func (a billingBelongsToAppTx) Append(values ...*model.App) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a billingBelongsToAppTx) Replace(values ...*model.App) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a billingBelongsToAppTx) Delete(values ...*model.App) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a billingBelongsToAppTx) Clear() error {
	return a.tx.Clear()
}

func (a billingBelongsToAppTx) Count() int64 {
	return a.tx.Count()
}

func (a billingBelongsToAppTx) Unscoped() *billingBelongsToAppTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type billingBelongsToUser struct {
	db *gorm.DB

	field.RelationField
}

func (a billingBelongsToUser) Where(conds ...field.Expr) *billingBelongsToUser {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a billingBelongsToUser) WithContext(ctx context.Context) *billingBelongsToUser {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a billingBelongsToUser) Session(session *gorm.Session) *billingBelongsToUser {
	a.db = a.db.Session(session)
	return &a
}

func (a billingBelongsToUser) Model(m *model.Billing) *billingBelongsToUserTx {
	return &billingBelongsToUserTx{a.db.Model(m).Association(a.Name())}
}

func (a billingBelongsToUser) Unscoped() *billingBelongsToUser {
	a.db = a.db.Unscoped()
	return &a
}

type billingBelongsToUserTx struct{ tx *gorm.Association }

func (a billingBelongsToUserTx) Find() (result *model.User, err error) {
	return result, a.tx.Find(&result)
}

func (a billingBelongsToUserTx) Append(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a billingBelongsToUserTx) Replace(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a billingBelongsToUserTx) Delete(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a billingBelongsToUserTx) Clear() error {
	return a.tx.Clear()
}

func (a billingBelongsToUserTx) Count() int64 {
	return a.tx.Count()
}

func (a billingBelongsToUserTx) Unscoped() *billingBelongsToUserTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type billingDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (b billingDo) FirstByID(id uint64) (result *model.Billing, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = b.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (b billingDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update billings set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = b.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (b billingDo) Debug() *billingDo {
	return b.withDO(b.DO.Debug())
}

func (b billingDo) WithContext(ctx context.Context) *billingDo {
	return b.withDO(b.DO.WithContext(ctx))
}

func (b billingDo) ReadDB() *billingDo {
	return b.Clauses(dbresolver.Read)
}

func (b billingDo) WriteDB() *billingDo {
	return b.Clauses(dbresolver.Write)
}

func (b billingDo) Session(config *gorm.Session) *billingDo {
	return b.withDO(b.DO.Session(config))
}

func (b billingDo) Clauses(conds ...clause.Expression) *billingDo {
	return b.withDO(b.DO.Clauses(conds...))
}

func (b billingDo) Returning(value interface{}, columns ...string) *billingDo {
	return b.withDO(b.DO.Returning(value, columns...))
}

func (b billingDo) Not(conds ...gen.Condition) *billingDo {
	return b.withDO(b.DO.Not(conds...))
}

func (b billingDo) Or(conds ...gen.Condition) *billingDo {
	return b.withDO(b.DO.Or(conds...))
}

func (b billingDo) Select(conds ...field.Expr) *billingDo {
	return b.withDO(b.DO.Select(conds...))
}

func (b billingDo) Where(conds ...gen.Condition) *billingDo {
	return b.withDO(b.DO.Where(conds...))
}

func (b billingDo) Order(conds ...field.Expr) *billingDo {
	return b.withDO(b.DO.Order(conds...))
}

func (b billingDo) Distinct(cols ...field.Expr) *billingDo {
	return b.withDO(b.DO.Distinct(cols...))
}

func (b billingDo) Omit(cols ...field.Expr) *billingDo {
	return b.withDO(b.DO.Omit(cols...))
}

func (b billingDo) Join(table schema.Tabler, on ...field.Expr) *billingDo {
	return b.withDO(b.DO.Join(table, on...))
}

func (b billingDo) LeftJoin(table schema.Tabler, on ...field.Expr) *billingDo {
	return b.withDO(b.DO.LeftJoin(table, on...))
}

func (b billingDo) RightJoin(table schema.Tabler, on ...field.Expr) *billingDo {
	return b.withDO(b.DO.RightJoin(table, on...))
}

func (b billingDo) Group(cols ...field.Expr) *billingDo {
	return b.withDO(b.DO.Group(cols...))
}

func (b billingDo) Having(conds ...gen.Condition) *billingDo {
	return b.withDO(b.DO.Having(conds...))
}

func (b billingDo) Limit(limit int) *billingDo {
	return b.withDO(b.DO.Limit(limit))
}

func (b billingDo) Offset(offset int) *billingDo {
	return b.withDO(b.DO.Offset(offset))
}

func (b billingDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *billingDo {
	return b.withDO(b.DO.Scopes(funcs...))
}

func (b billingDo) Unscoped() *billingDo {
	return b.withDO(b.DO.Unscoped())
}

func (b billingDo) Create(values ...*model.Billing) error {
	if len(values) == 0 {
		return nil
	}
	return b.DO.Create(values)
}

func (b billingDo) CreateInBatches(values []*model.Billing, batchSize int) error {
	return b.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (b billingDo) Save(values ...*model.Billing) error {
	if len(values) == 0 {
		return nil
	}
	return b.DO.Save(values)
}

func (b billingDo) First() (*model.Billing, error) {
	if result, err := b.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Billing), nil
	}
}

func (b billingDo) Take() (*model.Billing, error) {
	if result, err := b.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Billing), nil
	}
}

func (b billingDo) Last() (*model.Billing, error) {
	if result, err := b.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Billing), nil
	}
}

func (b billingDo) Find() ([]*model.Billing, error) {
	result, err := b.DO.Find()
	return result.([]*model.Billing), err
}

func (b billingDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Billing, err error) {
	buf := make([]*model.Billing, 0, batchSize)
	err = b.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (b billingDo) FindInBatches(result *[]*model.Billing, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return b.DO.FindInBatches(result, batchSize, fc)
}

func (b billingDo) Attrs(attrs ...field.AssignExpr) *billingDo {
	return b.withDO(b.DO.Attrs(attrs...))
}

func (b billingDo) Assign(attrs ...field.AssignExpr) *billingDo {
	return b.withDO(b.DO.Assign(attrs...))
}

func (b billingDo) Joins(fields ...field.RelationField) *billingDo {
	for _, _f := range fields {
		b = *b.withDO(b.DO.Joins(_f))
	}
	return &b
}

func (b billingDo) Preload(fields ...field.RelationField) *billingDo {
	for _, _f := range fields {
		b = *b.withDO(b.DO.Preload(_f))
	}
	return &b
}

func (b billingDo) FirstOrInit() (*model.Billing, error) {
	if result, err := b.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Billing), nil
	}
}

func (b billingDo) FirstOrCreate() (*model.Billing, error) {
	if result, err := b.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Billing), nil
	}
}

func (b billingDo) FindByPage(offset int, limit int) (result []*model.Billing, count int64, err error) {
	result, err = b.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = b.Offset(-1).Limit(-1).Count()
	return
}

func (b billingDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = b.Count()
	if err != nil {
		return
	}

	err = b.Offset(offset).Limit(limit).Scan(result)
	return
}

func (b billingDo) Scan(result interface{}) (err error) {
	return b.DO.Scan(result)
}

func (b billingDo) Delete(models ...*model.Billing) (result gen.ResultInfo, err error) {
	return b.DO.Delete(models)
}

func (b *billingDo) withDO(do gen.Dao) *billingDo {
	b.DO = *do.(*gen.DO)
	return b
}
