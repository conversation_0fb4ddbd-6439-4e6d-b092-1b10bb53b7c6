// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/potato-billing-api/model"
)

func newPricingRule(db *gorm.DB, opts ...gen.DOOption) pricingRule {
	_pricingRule := pricingRule{}

	_pricingRule.pricingRuleDo.UseDB(db, opts...)
	_pricingRule.pricingRuleDo.UseModel(&model.PricingRule{})

	tableName := _pricingRule.pricingRuleDo.TableName()
	_pricingRule.ALL = field.NewAsterisk(tableName)
	_pricingRule.ID = field.NewUint64(tableName, "id")
	_pricingRule.CreatedAt = field.NewInt64(tableName, "created_at")
	_pricingRule.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_pricingRule.DeletedAt = field.NewUint(tableName, "deleted_at")
	_pricingRule.Module = field.NewString(tableName, "module")
	_pricingRule.ModelName = field.NewString(tableName, "model_name")
	_pricingRule.UnitPrice = field.NewFloat64(tableName, "unit_price")
	_pricingRule.Currency = field.NewString(tableName, "currency")
	_pricingRule.Unit = field.NewString(tableName, "unit")
	_pricingRule.BaseUnit = field.NewInt64(tableName, "base_unit")
	_pricingRule.IsActive = field.NewBool(tableName, "is_active")
	_pricingRule.Priority = field.NewInt(tableName, "priority")
	_pricingRule.Description = field.NewString(tableName, "description")

	_pricingRule.fillFieldMap()

	return _pricingRule
}

type pricingRule struct {
	pricingRuleDo

	ALL         field.Asterisk
	ID          field.Uint64
	CreatedAt   field.Int64
	UpdatedAt   field.Int64
	DeletedAt   field.Uint
	Module      field.String
	ModelName   field.String
	UnitPrice   field.Float64
	Currency    field.String
	Unit        field.String
	BaseUnit    field.Int64
	IsActive    field.Bool
	Priority    field.Int
	Description field.String

	fieldMap map[string]field.Expr
}

func (p pricingRule) Table(newTableName string) *pricingRule {
	p.pricingRuleDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p pricingRule) As(alias string) *pricingRule {
	p.pricingRuleDo.DO = *(p.pricingRuleDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *pricingRule) updateTableName(table string) *pricingRule {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewUint64(table, "id")
	p.CreatedAt = field.NewInt64(table, "created_at")
	p.UpdatedAt = field.NewInt64(table, "updated_at")
	p.DeletedAt = field.NewUint(table, "deleted_at")
	p.Module = field.NewString(table, "module")
	p.ModelName = field.NewString(table, "model_name")
	p.UnitPrice = field.NewFloat64(table, "unit_price")
	p.Currency = field.NewString(table, "currency")
	p.Unit = field.NewString(table, "unit")
	p.BaseUnit = field.NewInt64(table, "base_unit")
	p.IsActive = field.NewBool(table, "is_active")
	p.Priority = field.NewInt(table, "priority")
	p.Description = field.NewString(table, "description")

	p.fillFieldMap()

	return p
}

func (p *pricingRule) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *pricingRule) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 13)
	p.fieldMap["id"] = p.ID
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["deleted_at"] = p.DeletedAt
	p.fieldMap["module"] = p.Module
	p.fieldMap["model_name"] = p.ModelName
	p.fieldMap["unit_price"] = p.UnitPrice
	p.fieldMap["currency"] = p.Currency
	p.fieldMap["unit"] = p.Unit
	p.fieldMap["base_unit"] = p.BaseUnit
	p.fieldMap["is_active"] = p.IsActive
	p.fieldMap["priority"] = p.Priority
	p.fieldMap["description"] = p.Description
}

func (p pricingRule) clone(db *gorm.DB) pricingRule {
	p.pricingRuleDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p pricingRule) replaceDB(db *gorm.DB) pricingRule {
	p.pricingRuleDo.ReplaceDB(db)
	return p
}

type pricingRuleDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (p pricingRuleDo) FirstByID(id uint64) (result *model.PricingRule, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = p.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (p pricingRuleDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update pricing_rules set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = p.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (p pricingRuleDo) Debug() *pricingRuleDo {
	return p.withDO(p.DO.Debug())
}

func (p pricingRuleDo) WithContext(ctx context.Context) *pricingRuleDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p pricingRuleDo) ReadDB() *pricingRuleDo {
	return p.Clauses(dbresolver.Read)
}

func (p pricingRuleDo) WriteDB() *pricingRuleDo {
	return p.Clauses(dbresolver.Write)
}

func (p pricingRuleDo) Session(config *gorm.Session) *pricingRuleDo {
	return p.withDO(p.DO.Session(config))
}

func (p pricingRuleDo) Clauses(conds ...clause.Expression) *pricingRuleDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p pricingRuleDo) Returning(value interface{}, columns ...string) *pricingRuleDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p pricingRuleDo) Not(conds ...gen.Condition) *pricingRuleDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p pricingRuleDo) Or(conds ...gen.Condition) *pricingRuleDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p pricingRuleDo) Select(conds ...field.Expr) *pricingRuleDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p pricingRuleDo) Where(conds ...gen.Condition) *pricingRuleDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p pricingRuleDo) Order(conds ...field.Expr) *pricingRuleDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p pricingRuleDo) Distinct(cols ...field.Expr) *pricingRuleDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p pricingRuleDo) Omit(cols ...field.Expr) *pricingRuleDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p pricingRuleDo) Join(table schema.Tabler, on ...field.Expr) *pricingRuleDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p pricingRuleDo) LeftJoin(table schema.Tabler, on ...field.Expr) *pricingRuleDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p pricingRuleDo) RightJoin(table schema.Tabler, on ...field.Expr) *pricingRuleDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p pricingRuleDo) Group(cols ...field.Expr) *pricingRuleDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p pricingRuleDo) Having(conds ...gen.Condition) *pricingRuleDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p pricingRuleDo) Limit(limit int) *pricingRuleDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p pricingRuleDo) Offset(offset int) *pricingRuleDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p pricingRuleDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *pricingRuleDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p pricingRuleDo) Unscoped() *pricingRuleDo {
	return p.withDO(p.DO.Unscoped())
}

func (p pricingRuleDo) Create(values ...*model.PricingRule) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p pricingRuleDo) CreateInBatches(values []*model.PricingRule, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p pricingRuleDo) Save(values ...*model.PricingRule) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p pricingRuleDo) First() (*model.PricingRule, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.PricingRule), nil
	}
}

func (p pricingRuleDo) Take() (*model.PricingRule, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.PricingRule), nil
	}
}

func (p pricingRuleDo) Last() (*model.PricingRule, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.PricingRule), nil
	}
}

func (p pricingRuleDo) Find() ([]*model.PricingRule, error) {
	result, err := p.DO.Find()
	return result.([]*model.PricingRule), err
}

func (p pricingRuleDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.PricingRule, err error) {
	buf := make([]*model.PricingRule, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p pricingRuleDo) FindInBatches(result *[]*model.PricingRule, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p pricingRuleDo) Attrs(attrs ...field.AssignExpr) *pricingRuleDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p pricingRuleDo) Assign(attrs ...field.AssignExpr) *pricingRuleDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p pricingRuleDo) Joins(fields ...field.RelationField) *pricingRuleDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p pricingRuleDo) Preload(fields ...field.RelationField) *pricingRuleDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p pricingRuleDo) FirstOrInit() (*model.PricingRule, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.PricingRule), nil
	}
}

func (p pricingRuleDo) FirstOrCreate() (*model.PricingRule, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.PricingRule), nil
	}
}

func (p pricingRuleDo) FindByPage(offset int, limit int) (result []*model.PricingRule, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p pricingRuleDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p pricingRuleDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p pricingRuleDo) Delete(models ...*model.PricingRule) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *pricingRuleDo) withDO(do gen.Dao) *pricingRuleDo {
	p.DO = *do.(*gen.DO)
	return p
}
