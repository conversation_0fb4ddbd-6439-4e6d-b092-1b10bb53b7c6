// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/potato-billing-api/model"
)

func newQuotaPackageRecord(db *gorm.DB, opts ...gen.DOOption) quotaPackageRecord {
	_quotaPackageRecord := quotaPackageRecord{}

	_quotaPackageRecord.quotaPackageRecordDo.UseDB(db, opts...)
	_quotaPackageRecord.quotaPackageRecordDo.UseModel(&model.QuotaPackageRecord{})

	tableName := _quotaPackageRecord.quotaPackageRecordDo.TableName()
	_quotaPackageRecord.ALL = field.NewAsterisk(tableName)
	_quotaPackageRecord.ID = field.NewUint64(tableName, "id")
	_quotaPackageRecord.CreatedAt = field.NewInt64(tableName, "created_at")
	_quotaPackageRecord.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_quotaPackageRecord.DeletedAt = field.NewUint(tableName, "deleted_at")
	_quotaPackageRecord.UserID = field.NewUint64(tableName, "user_id")
	_quotaPackageRecord.AppID = field.NewUint64(tableName, "app_id")
	_quotaPackageRecord.Module = field.NewString(tableName, "module")
	_quotaPackageRecord.ModelName = field.NewString(tableName, "model_name")
	_quotaPackageRecord.Quota = field.NewInt64(tableName, "quota")
	_quotaPackageRecord.Used = field.NewInt64(tableName, "used")
	_quotaPackageRecord.ExpiresAt = field.NewInt64(tableName, "expires_at")
	_quotaPackageRecord.Status = field.NewString(tableName, "status")
	_quotaPackageRecord.Type = field.NewString(tableName, "type")
	_quotaPackageRecord.Description = field.NewString(tableName, "description")
	_quotaPackageRecord.Metadata = field.NewField(tableName, "metadata")
	_quotaPackageRecord.OperatorID = field.NewUint64(tableName, "operator_id")
	_quotaPackageRecord.User = quotaPackageRecordBelongsToUser{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("User", "model.User"),
		Avatar: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("User.Avatar", "model.Upload"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("User.Avatar.User", "model.User"),
			},
		},
	}

	_quotaPackageRecord.App = quotaPackageRecordBelongsToApp{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("App", "model.App"),
		User: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("App.User", "model.User"),
		},
	}

	_quotaPackageRecord.Operator = quotaPackageRecordBelongsToOperator{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Operator", "model.User"),
	}

	_quotaPackageRecord.fillFieldMap()

	return _quotaPackageRecord
}

type quotaPackageRecord struct {
	quotaPackageRecordDo

	ALL         field.Asterisk
	ID          field.Uint64
	CreatedAt   field.Int64
	UpdatedAt   field.Int64
	DeletedAt   field.Uint
	UserID      field.Uint64
	AppID       field.Uint64
	Module      field.String
	ModelName   field.String
	Quota       field.Int64
	Used        field.Int64
	ExpiresAt   field.Int64
	Status      field.String
	Type        field.String
	Description field.String
	Metadata    field.Field
	OperatorID  field.Uint64
	User        quotaPackageRecordBelongsToUser

	App quotaPackageRecordBelongsToApp

	Operator quotaPackageRecordBelongsToOperator

	fieldMap map[string]field.Expr
}

func (q quotaPackageRecord) Table(newTableName string) *quotaPackageRecord {
	q.quotaPackageRecordDo.UseTable(newTableName)
	return q.updateTableName(newTableName)
}

func (q quotaPackageRecord) As(alias string) *quotaPackageRecord {
	q.quotaPackageRecordDo.DO = *(q.quotaPackageRecordDo.As(alias).(*gen.DO))
	return q.updateTableName(alias)
}

func (q *quotaPackageRecord) updateTableName(table string) *quotaPackageRecord {
	q.ALL = field.NewAsterisk(table)
	q.ID = field.NewUint64(table, "id")
	q.CreatedAt = field.NewInt64(table, "created_at")
	q.UpdatedAt = field.NewInt64(table, "updated_at")
	q.DeletedAt = field.NewUint(table, "deleted_at")
	q.UserID = field.NewUint64(table, "user_id")
	q.AppID = field.NewUint64(table, "app_id")
	q.Module = field.NewString(table, "module")
	q.ModelName = field.NewString(table, "model_name")
	q.Quota = field.NewInt64(table, "quota")
	q.Used = field.NewInt64(table, "used")
	q.ExpiresAt = field.NewInt64(table, "expires_at")
	q.Status = field.NewString(table, "status")
	q.Type = field.NewString(table, "type")
	q.Description = field.NewString(table, "description")
	q.Metadata = field.NewField(table, "metadata")
	q.OperatorID = field.NewUint64(table, "operator_id")

	q.fillFieldMap()

	return q
}

func (q *quotaPackageRecord) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := q.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (q *quotaPackageRecord) fillFieldMap() {
	q.fieldMap = make(map[string]field.Expr, 19)
	q.fieldMap["id"] = q.ID
	q.fieldMap["created_at"] = q.CreatedAt
	q.fieldMap["updated_at"] = q.UpdatedAt
	q.fieldMap["deleted_at"] = q.DeletedAt
	q.fieldMap["user_id"] = q.UserID
	q.fieldMap["app_id"] = q.AppID
	q.fieldMap["module"] = q.Module
	q.fieldMap["model_name"] = q.ModelName
	q.fieldMap["quota"] = q.Quota
	q.fieldMap["used"] = q.Used
	q.fieldMap["expires_at"] = q.ExpiresAt
	q.fieldMap["status"] = q.Status
	q.fieldMap["type"] = q.Type
	q.fieldMap["description"] = q.Description
	q.fieldMap["metadata"] = q.Metadata
	q.fieldMap["operator_id"] = q.OperatorID

}

func (q quotaPackageRecord) clone(db *gorm.DB) quotaPackageRecord {
	q.quotaPackageRecordDo.ReplaceConnPool(db.Statement.ConnPool)
	q.User.db = db.Session(&gorm.Session{Initialized: true})
	q.User.db.Statement.ConnPool = db.Statement.ConnPool
	q.App.db = db.Session(&gorm.Session{Initialized: true})
	q.App.db.Statement.ConnPool = db.Statement.ConnPool
	q.Operator.db = db.Session(&gorm.Session{Initialized: true})
	q.Operator.db.Statement.ConnPool = db.Statement.ConnPool
	return q
}

func (q quotaPackageRecord) replaceDB(db *gorm.DB) quotaPackageRecord {
	q.quotaPackageRecordDo.ReplaceDB(db)
	q.User.db = db.Session(&gorm.Session{})
	q.App.db = db.Session(&gorm.Session{})
	q.Operator.db = db.Session(&gorm.Session{})
	return q
}

type quotaPackageRecordBelongsToUser struct {
	db *gorm.DB

	field.RelationField

	Avatar struct {
		field.RelationField
		User struct {
			field.RelationField
		}
	}
}

func (a quotaPackageRecordBelongsToUser) Where(conds ...field.Expr) *quotaPackageRecordBelongsToUser {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a quotaPackageRecordBelongsToUser) WithContext(ctx context.Context) *quotaPackageRecordBelongsToUser {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a quotaPackageRecordBelongsToUser) Session(session *gorm.Session) *quotaPackageRecordBelongsToUser {
	a.db = a.db.Session(session)
	return &a
}

func (a quotaPackageRecordBelongsToUser) Model(m *model.QuotaPackageRecord) *quotaPackageRecordBelongsToUserTx {
	return &quotaPackageRecordBelongsToUserTx{a.db.Model(m).Association(a.Name())}
}

func (a quotaPackageRecordBelongsToUser) Unscoped() *quotaPackageRecordBelongsToUser {
	a.db = a.db.Unscoped()
	return &a
}

type quotaPackageRecordBelongsToUserTx struct{ tx *gorm.Association }

func (a quotaPackageRecordBelongsToUserTx) Find() (result *model.User, err error) {
	return result, a.tx.Find(&result)
}

func (a quotaPackageRecordBelongsToUserTx) Append(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a quotaPackageRecordBelongsToUserTx) Replace(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a quotaPackageRecordBelongsToUserTx) Delete(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a quotaPackageRecordBelongsToUserTx) Clear() error {
	return a.tx.Clear()
}

func (a quotaPackageRecordBelongsToUserTx) Count() int64 {
	return a.tx.Count()
}

func (a quotaPackageRecordBelongsToUserTx) Unscoped() *quotaPackageRecordBelongsToUserTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type quotaPackageRecordBelongsToApp struct {
	db *gorm.DB

	field.RelationField

	User struct {
		field.RelationField
	}
}

func (a quotaPackageRecordBelongsToApp) Where(conds ...field.Expr) *quotaPackageRecordBelongsToApp {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a quotaPackageRecordBelongsToApp) WithContext(ctx context.Context) *quotaPackageRecordBelongsToApp {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a quotaPackageRecordBelongsToApp) Session(session *gorm.Session) *quotaPackageRecordBelongsToApp {
	a.db = a.db.Session(session)
	return &a
}

func (a quotaPackageRecordBelongsToApp) Model(m *model.QuotaPackageRecord) *quotaPackageRecordBelongsToAppTx {
	return &quotaPackageRecordBelongsToAppTx{a.db.Model(m).Association(a.Name())}
}

func (a quotaPackageRecordBelongsToApp) Unscoped() *quotaPackageRecordBelongsToApp {
	a.db = a.db.Unscoped()
	return &a
}

type quotaPackageRecordBelongsToAppTx struct{ tx *gorm.Association }

func (a quotaPackageRecordBelongsToAppTx) Find() (result *model.App, err error) {
	return result, a.tx.Find(&result)
}

func (a quotaPackageRecordBelongsToAppTx) Append(values ...*model.App) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a quotaPackageRecordBelongsToAppTx) Replace(values ...*model.App) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a quotaPackageRecordBelongsToAppTx) Delete(values ...*model.App) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a quotaPackageRecordBelongsToAppTx) Clear() error {
	return a.tx.Clear()
}

func (a quotaPackageRecordBelongsToAppTx) Count() int64 {
	return a.tx.Count()
}

func (a quotaPackageRecordBelongsToAppTx) Unscoped() *quotaPackageRecordBelongsToAppTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type quotaPackageRecordBelongsToOperator struct {
	db *gorm.DB

	field.RelationField
}

func (a quotaPackageRecordBelongsToOperator) Where(conds ...field.Expr) *quotaPackageRecordBelongsToOperator {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a quotaPackageRecordBelongsToOperator) WithContext(ctx context.Context) *quotaPackageRecordBelongsToOperator {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a quotaPackageRecordBelongsToOperator) Session(session *gorm.Session) *quotaPackageRecordBelongsToOperator {
	a.db = a.db.Session(session)
	return &a
}

func (a quotaPackageRecordBelongsToOperator) Model(m *model.QuotaPackageRecord) *quotaPackageRecordBelongsToOperatorTx {
	return &quotaPackageRecordBelongsToOperatorTx{a.db.Model(m).Association(a.Name())}
}

func (a quotaPackageRecordBelongsToOperator) Unscoped() *quotaPackageRecordBelongsToOperator {
	a.db = a.db.Unscoped()
	return &a
}

type quotaPackageRecordBelongsToOperatorTx struct{ tx *gorm.Association }

func (a quotaPackageRecordBelongsToOperatorTx) Find() (result *model.User, err error) {
	return result, a.tx.Find(&result)
}

func (a quotaPackageRecordBelongsToOperatorTx) Append(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a quotaPackageRecordBelongsToOperatorTx) Replace(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a quotaPackageRecordBelongsToOperatorTx) Delete(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a quotaPackageRecordBelongsToOperatorTx) Clear() error {
	return a.tx.Clear()
}

func (a quotaPackageRecordBelongsToOperatorTx) Count() int64 {
	return a.tx.Count()
}

func (a quotaPackageRecordBelongsToOperatorTx) Unscoped() *quotaPackageRecordBelongsToOperatorTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type quotaPackageRecordDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (q quotaPackageRecordDo) FirstByID(id uint64) (result *model.QuotaPackageRecord, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = q.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (q quotaPackageRecordDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update quota_package_records set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = q.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (q quotaPackageRecordDo) Debug() *quotaPackageRecordDo {
	return q.withDO(q.DO.Debug())
}

func (q quotaPackageRecordDo) WithContext(ctx context.Context) *quotaPackageRecordDo {
	return q.withDO(q.DO.WithContext(ctx))
}

func (q quotaPackageRecordDo) ReadDB() *quotaPackageRecordDo {
	return q.Clauses(dbresolver.Read)
}

func (q quotaPackageRecordDo) WriteDB() *quotaPackageRecordDo {
	return q.Clauses(dbresolver.Write)
}

func (q quotaPackageRecordDo) Session(config *gorm.Session) *quotaPackageRecordDo {
	return q.withDO(q.DO.Session(config))
}

func (q quotaPackageRecordDo) Clauses(conds ...clause.Expression) *quotaPackageRecordDo {
	return q.withDO(q.DO.Clauses(conds...))
}

func (q quotaPackageRecordDo) Returning(value interface{}, columns ...string) *quotaPackageRecordDo {
	return q.withDO(q.DO.Returning(value, columns...))
}

func (q quotaPackageRecordDo) Not(conds ...gen.Condition) *quotaPackageRecordDo {
	return q.withDO(q.DO.Not(conds...))
}

func (q quotaPackageRecordDo) Or(conds ...gen.Condition) *quotaPackageRecordDo {
	return q.withDO(q.DO.Or(conds...))
}

func (q quotaPackageRecordDo) Select(conds ...field.Expr) *quotaPackageRecordDo {
	return q.withDO(q.DO.Select(conds...))
}

func (q quotaPackageRecordDo) Where(conds ...gen.Condition) *quotaPackageRecordDo {
	return q.withDO(q.DO.Where(conds...))
}

func (q quotaPackageRecordDo) Order(conds ...field.Expr) *quotaPackageRecordDo {
	return q.withDO(q.DO.Order(conds...))
}

func (q quotaPackageRecordDo) Distinct(cols ...field.Expr) *quotaPackageRecordDo {
	return q.withDO(q.DO.Distinct(cols...))
}

func (q quotaPackageRecordDo) Omit(cols ...field.Expr) *quotaPackageRecordDo {
	return q.withDO(q.DO.Omit(cols...))
}

func (q quotaPackageRecordDo) Join(table schema.Tabler, on ...field.Expr) *quotaPackageRecordDo {
	return q.withDO(q.DO.Join(table, on...))
}

func (q quotaPackageRecordDo) LeftJoin(table schema.Tabler, on ...field.Expr) *quotaPackageRecordDo {
	return q.withDO(q.DO.LeftJoin(table, on...))
}

func (q quotaPackageRecordDo) RightJoin(table schema.Tabler, on ...field.Expr) *quotaPackageRecordDo {
	return q.withDO(q.DO.RightJoin(table, on...))
}

func (q quotaPackageRecordDo) Group(cols ...field.Expr) *quotaPackageRecordDo {
	return q.withDO(q.DO.Group(cols...))
}

func (q quotaPackageRecordDo) Having(conds ...gen.Condition) *quotaPackageRecordDo {
	return q.withDO(q.DO.Having(conds...))
}

func (q quotaPackageRecordDo) Limit(limit int) *quotaPackageRecordDo {
	return q.withDO(q.DO.Limit(limit))
}

func (q quotaPackageRecordDo) Offset(offset int) *quotaPackageRecordDo {
	return q.withDO(q.DO.Offset(offset))
}

func (q quotaPackageRecordDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *quotaPackageRecordDo {
	return q.withDO(q.DO.Scopes(funcs...))
}

func (q quotaPackageRecordDo) Unscoped() *quotaPackageRecordDo {
	return q.withDO(q.DO.Unscoped())
}

func (q quotaPackageRecordDo) Create(values ...*model.QuotaPackageRecord) error {
	if len(values) == 0 {
		return nil
	}
	return q.DO.Create(values)
}

func (q quotaPackageRecordDo) CreateInBatches(values []*model.QuotaPackageRecord, batchSize int) error {
	return q.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (q quotaPackageRecordDo) Save(values ...*model.QuotaPackageRecord) error {
	if len(values) == 0 {
		return nil
	}
	return q.DO.Save(values)
}

func (q quotaPackageRecordDo) First() (*model.QuotaPackageRecord, error) {
	if result, err := q.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.QuotaPackageRecord), nil
	}
}

func (q quotaPackageRecordDo) Take() (*model.QuotaPackageRecord, error) {
	if result, err := q.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.QuotaPackageRecord), nil
	}
}

func (q quotaPackageRecordDo) Last() (*model.QuotaPackageRecord, error) {
	if result, err := q.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.QuotaPackageRecord), nil
	}
}

func (q quotaPackageRecordDo) Find() ([]*model.QuotaPackageRecord, error) {
	result, err := q.DO.Find()
	return result.([]*model.QuotaPackageRecord), err
}

func (q quotaPackageRecordDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.QuotaPackageRecord, err error) {
	buf := make([]*model.QuotaPackageRecord, 0, batchSize)
	err = q.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (q quotaPackageRecordDo) FindInBatches(result *[]*model.QuotaPackageRecord, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return q.DO.FindInBatches(result, batchSize, fc)
}

func (q quotaPackageRecordDo) Attrs(attrs ...field.AssignExpr) *quotaPackageRecordDo {
	return q.withDO(q.DO.Attrs(attrs...))
}

func (q quotaPackageRecordDo) Assign(attrs ...field.AssignExpr) *quotaPackageRecordDo {
	return q.withDO(q.DO.Assign(attrs...))
}

func (q quotaPackageRecordDo) Joins(fields ...field.RelationField) *quotaPackageRecordDo {
	for _, _f := range fields {
		q = *q.withDO(q.DO.Joins(_f))
	}
	return &q
}

func (q quotaPackageRecordDo) Preload(fields ...field.RelationField) *quotaPackageRecordDo {
	for _, _f := range fields {
		q = *q.withDO(q.DO.Preload(_f))
	}
	return &q
}

func (q quotaPackageRecordDo) FirstOrInit() (*model.QuotaPackageRecord, error) {
	if result, err := q.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.QuotaPackageRecord), nil
	}
}

func (q quotaPackageRecordDo) FirstOrCreate() (*model.QuotaPackageRecord, error) {
	if result, err := q.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.QuotaPackageRecord), nil
	}
}

func (q quotaPackageRecordDo) FindByPage(offset int, limit int) (result []*model.QuotaPackageRecord, count int64, err error) {
	result, err = q.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = q.Offset(-1).Limit(-1).Count()
	return
}

func (q quotaPackageRecordDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = q.Count()
	if err != nil {
		return
	}

	err = q.Offset(offset).Limit(limit).Scan(result)
	return
}

func (q quotaPackageRecordDo) Scan(result interface{}) (err error) {
	return q.DO.Scan(result)
}

func (q quotaPackageRecordDo) Delete(models ...*model.QuotaPackageRecord) (result gen.ResultInfo, err error) {
	return q.DO.Delete(models)
}

func (q *quotaPackageRecordDo) withDO(do gen.Dao) *quotaPackageRecordDo {
	q.DO = *do.(*gen.DO)
	return q
}
