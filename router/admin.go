package router

import (
	"git.uozi.org/uozi/potato-billing-api/api/admin/billing"
	"git.uozi.org/uozi/potato-billing-api/api/admin/settings"
	"git.uozi.org/uozi/potato-billing-api/api/admin/statistics"
	"git.uozi.org/uozi/potato-billing-api/api/admin/user"
	"github.com/uozi-tech/cosy"
)

func initAdminRouter() {
	r := cosy.GetEngine()
	admin := r.Group("/admin/", AuthRequired())
	{
		user.InitUserRouter(admin)
		settings.InitRouter(admin)
		billing.RegisterRoutes(admin)
		statistics.RegisterStatisticsRouter(admin)
	}
}
