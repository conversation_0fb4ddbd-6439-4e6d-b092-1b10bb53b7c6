package router

import (
	"git.uozi.org/uozi/potato-billing-api/api/frontend/app"
	"git.uozi.org/uozi/potato-billing-api/api/frontend/billing"
	"github.com/uozi-tech/cosy"
)

func initFrontendRouter() {
	r := cosy.GetEngine()

	// 客户端API路由 - 需要认证
	clientAPI := r.Group("/client", AuthRequired())
	{
		app.InitRouter(clientAPI)
		
		// billing相关接口
		billingAPI := clientAPI.Group("/billing")
		billing.RegisterStatsRouter(billingAPI)
		
		// 应用级别的billing接口
		billing.RegisterApplicationBillingRouter(clientAPI)
	}
}
