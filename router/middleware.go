package router

import (
	"bytes"
	"context"
	"net/http"

	"git.uozi.org/uozi/potato-billing-api/internal/limiter"
	"git.uozi.org/uozi/potato-billing-api/internal/user"
	"git.uozi.org/uozi/potato-billing-api/model"
	rate_limiter "git.uozi.org/uozi/rate-limiter-go"
	"github.com/gin-gonic/gin"
)

func AuthRequired() gin.HandlerFunc {
	return func(c *gin.Context) {
		u, err := user.CurrentUser(c)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"message": "unauthorized",
				"error":   err.<PERSON>rror(),
			})
			return
		}

		if u.Status == model.UserStatusBlocked {
			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{
				"message": "this user is blocked",
			})
			return
		}

		c.<PERSON>("user", u)

		c.Next()
	}
}

func buildLimiterKey(c *gin.Context) string {
	return "limiter:" + c.<PERSON>() + ":" + c.ClientIP() + ":" + c.<PERSON>("X-Fingerprint")
}

type responseWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func LimiterMiddleware(conf *rate_limiter.LimitConf) gin.HandlerFunc {
	return func(c *gin.Context) {
		key := buildLimiterKey(c)

		lm := limiter.GetLimiter()
		result, err := lm.Allow(context.Background(), key, conf)
		if err != nil {
			return
		}
		if result.Allowed == 0 && result.Remaining == 0 {
			c.AbortWithStatusJSON(http.StatusTooManyRequests, gin.H{
				"message": "Your operation is too frequent",
			})
			return
		}

		c.Next()

		//// 如果对所有请求都进行限流，则直接放行
		//if onlySuccess {
		//	c.Next()
		//	return
		//}

		//responseBodyWriter := &responseWriter{
		//	body:           bytes.NewBufferString(""),
		//	ResponseWriter: c.Writer,
		//}
		//c.Writer = responseBodyWriter
		//
		//c.Next()

		//respStatusCode := c.Writer.Status()
		//go func() {
		//	if respStatusCode != http.StatusNoContent && respStatusCode != http.StatusOK {
		//	}
		//}()
	}
}
